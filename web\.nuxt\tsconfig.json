// Generated by nuxi
{
  "compilerOptions": {
    "forceConsistentCasingInFileNames": true,
    "jsx": "preserve",
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "Node",
    "skipLibCheck": true,
    "strict": true,
    "allowJs": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "allowSyntheticDefaultImports": true,
    "types": [
      "node"
    ],
    "baseUrl": "..",
    "paths": {
      "~": [
        "."
      ],
      "~/*": [
        "./*"
      ],
      "@": [
        "."
      ],
      "@/*": [
        "./*"
      ],
      "~~": [
        "."
      ],
      "~~/*": [
        "./*"
      ],
      "@@": [
        "."
      ],
      "@@/*": [
        "./*"
      ],
      "assets": [
        "assets"
      ],
      "assets/*": [
        "assets/*"
      ],
      "public": [
        "public"
      ],
      "#app": [
        "node_modules/nuxt/dist/app"
      ],
      "#app/*": [
        "node_modules/nuxt/dist/app/*"
      ],
      "vue-demi": [
        "node_modules/nuxt/dist/app/compat/vue-demi"
      ],
      "#imports": [
        ".nuxt/imports"
      ],
      "#build": [
        ".nuxt"
      ],
      "#build/*": [
        ".nuxt/*"
      ],
      "#components": [
        ".nuxt/components"
      ]
    }
  },
  "include": [
    "./nuxt.d.ts",
    "../**/*"
  ],
  "exclude": [
    "../dist",
    "../.output"
  ]
}