// Generated by components discovery
declare module 'vue' {
  export interface GlobalComponents {
    'Icon': typeof import("../components/icon/index.vue")['default']
    'LoginDialog': typeof import("../components/login-dialog/index.vue")['default']
    'Login': typeof import("../components/login-dialog/login.vue")['default']
    'LoginDialogRegister': typeof import("../components/login-dialog/register.vue")['default']
    'Sidebar': typeof import("../components/sidebar/index.vue")['default']
    'SmsCode': typeof import("../components/sms-code/index.vue")['default']
    'UploadFile': typeof import("../components/upload-file/index.vue")['default']
    'NuxtWelcome': typeof import("../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
    'NuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/layout")['default']
    'NuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'ClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'DevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'ServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'NuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'NuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'ElAffix': typeof import("element-plus/es/components/affix/index")['ElAffix']
    'ElAlert': typeof import("element-plus/es/components/alert/index")['ElAlert']
    'ElAutocomplete': typeof import("element-plus/es/components/autocomplete/index")['ElAutocomplete']
    'ElAutoResizer': typeof import("element-plus/es/components/table-v2/index")['ElAutoResizer']
    'ElAvatar': typeof import("element-plus/es/components/avatar/index")['ElAvatar']
    'ElBacktop': typeof import("element-plus/es/components/backtop/index")['ElBacktop']
    'ElBadge': typeof import("element-plus/es/components/badge/index")['ElBadge']
    'ElBreadcrumb': typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumb']
    'ElBreadcrumbItem': typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumbItem']
    'ElButton': typeof import("element-plus/es/components/button/index")['ElButton']
    'ElButtonGroup': typeof import("element-plus/es/components/button/index")['ElButtonGroup']
    'ElCalendar': typeof import("element-plus/es/components/calendar/index")['ElCalendar']
    'ElCard': typeof import("element-plus/es/components/card/index")['ElCard']
    'ElCarousel': typeof import("element-plus/es/components/carousel/index")['ElCarousel']
    'ElCarouselItem': typeof import("element-plus/es/components/carousel/index")['ElCarouselItem']
    'ElCascader': typeof import("element-plus/es/components/cascader/index")['ElCascader']
    'ElCascaderPanel': typeof import("element-plus/es/components/cascader-panel/index")['ElCascaderPanel']
    'ElCheckTag': typeof import("element-plus/es/components/check-tag/index")['ElCheckTag']
    'ElCheckbox': typeof import("element-plus/es/components/checkbox/index")['ElCheckbox']
    'ElCheckboxButton': typeof import("element-plus/es/components/checkbox/index")['ElCheckboxButton']
    'ElCheckboxGroup': typeof import("element-plus/es/components/checkbox/index")['ElCheckboxGroup']
    'ElCol': typeof import("element-plus/es/components/col/index")['ElCol']
    'ElCollapse': typeof import("element-plus/es/components/collapse/index")['ElCollapse']
    'ElCollapseItem': typeof import("element-plus/es/components/collapse/index")['ElCollapseItem']
    'ElCollapseTransition': typeof import("element-plus/es/components/collapse-transition/index")['ElCollapseTransition']
    'ElColorPicker': typeof import("element-plus/es/components/color-picker/index")['ElColorPicker']
    'ElConfigProvider': typeof import("element-plus/es/components/config-provider/index")['ElConfigProvider']
    'ElContainer': typeof import("element-plus/es/components/container/index")['ElContainer']
    'ElAside': typeof import("element-plus/es/components/container/index")['ElAside']
    'ElFooter': typeof import("element-plus/es/components/container/index")['ElFooter']
    'ElHeader': typeof import("element-plus/es/components/container/index")['ElHeader']
    'ElMain': typeof import("element-plus/es/components/container/index")['ElMain']
    'ElDatePicker': typeof import("element-plus/es/components/date-picker/index")['ElDatePicker']
    'ElDescriptions': typeof import("element-plus/es/components/descriptions/index")['ElDescriptions']
    'ElDescriptionsItem': typeof import("element-plus/es/components/descriptions/index")['ElDescriptionsItem']
    'ElDialog': typeof import("element-plus/es/components/dialog/index")['ElDialog']
    'ElDivider': typeof import("element-plus/es/components/divider/index")['ElDivider']
    'ElDrawer': typeof import("element-plus/es/components/drawer/index")['ElDrawer']
    'ElDropdown': typeof import("element-plus/es/components/dropdown/index")['ElDropdown']
    'ElDropdownItem': typeof import("element-plus/es/components/dropdown/index")['ElDropdownItem']
    'ElDropdownMenu': typeof import("element-plus/es/components/dropdown/index")['ElDropdownMenu']
    'ElEmpty': typeof import("element-plus/es/components/empty/index")['ElEmpty']
    'ElForm': typeof import("element-plus/es/components/form/index")['ElForm']
    'ElFormItem': typeof import("element-plus/es/components/form/index")['ElFormItem']
    'ElIcon': typeof import("element-plus/es/components/icon/index")['ElIcon']
    'ElImage': typeof import("element-plus/es/components/image/index")['ElImage']
    'ElImageViewer': typeof import("element-plus/es/components/image-viewer/index")['ElImageViewer']
    'ElInput': typeof import("element-plus/es/components/input/index")['ElInput']
    'ElInputNumber': typeof import("element-plus/es/components/input-number/index")['ElInputNumber']
    'ElLink': typeof import("element-plus/es/components/link/index")['ElLink']
    'ElMenu': typeof import("element-plus/es/components/menu/index")['ElMenu']
    'ElMenuItem': typeof import("element-plus/es/components/menu/index")['ElMenuItem']
    'ElMenuItemGroup': typeof import("element-plus/es/components/menu/index")['ElMenuItemGroup']
    'ElSubMenu': typeof import("element-plus/es/components/menu/index")['ElSubMenu']
    'ElPageHeader': typeof import("element-plus/es/components/page-header/index")['ElPageHeader']
    'ElPagination': typeof import("element-plus/es/components/pagination/index")['ElPagination']
    'ElPopconfirm': typeof import("element-plus/es/components/popconfirm/index")['ElPopconfirm']
    'ElPopover': typeof import("element-plus/es/components/popover/index")['ElPopover']
    'ElPopper': typeof import("element-plus/es/components/popper/index")['ElPopper']
    'ElProgress': typeof import("element-plus/es/components/progress/index")['ElProgress']
    'ElRadio': typeof import("element-plus/es/components/radio/index")['ElRadio']
    'ElRadioButton': typeof import("element-plus/es/components/radio/index")['ElRadioButton']
    'ElRadioGroup': typeof import("element-plus/es/components/radio/index")['ElRadioGroup']
    'ElRate': typeof import("element-plus/es/components/rate/index")['ElRate']
    'ElResult': typeof import("element-plus/es/components/result/index")['ElResult']
    'ElRow': typeof import("element-plus/es/components/row/index")['ElRow']
    'ElScrollbar': typeof import("element-plus/es/components/scrollbar/index")['ElScrollbar']
    'ElSelect': typeof import("element-plus/es/components/select/index")['ElSelect']
    'ElOption': typeof import("element-plus/es/components/select/index")['ElOption']
    'ElOptionGroup': typeof import("element-plus/es/components/select/index")['ElOptionGroup']
    'ElSelectV2': typeof import("element-plus/es/components/select-v2/index")['ElSelectV2']
    'ElSkeleton': typeof import("element-plus/es/components/skeleton/index")['ElSkeleton']
    'ElSkeletonItem': typeof import("element-plus/es/components/skeleton/index")['ElSkeletonItem']
    'ElSlider': typeof import("element-plus/es/components/slider/index")['ElSlider']
    'ElSpace': typeof import("element-plus/es/components/space/index")['ElSpace']
    'ElStatistic': typeof import("element-plus/es/components/statistic/index")['ElStatistic']
    'ElCountdown': typeof import("element-plus/es/components/countdown/index")['ElCountdown']
    'ElSteps': typeof import("element-plus/es/components/steps/index")['ElSteps']
    'ElStep': typeof import("element-plus/es/components/steps/index")['ElStep']
    'ElSwitch': typeof import("element-plus/es/components/switch/index")['ElSwitch']
    'ElTable': typeof import("element-plus/es/components/table/index")['ElTable']
    'ElTableColumn': typeof import("element-plus/es/components/table/index")['ElTableColumn']
    'ElTableV2': typeof import("element-plus/es/components/table-v2/index")['ElTableV2']
    'ElTabs': typeof import("element-plus/es/components/tabs/index")['ElTabs']
    'ElTabPane': typeof import("element-plus/es/components/tabs/index")['ElTabPane']
    'ElTag': typeof import("element-plus/es/components/tag/index")['ElTag']
    'ElText': typeof import("element-plus/es/components/text/index")['ElText']
    'ElTimePicker': typeof import("element-plus/es/components/time-picker/index")['ElTimePicker']
    'ElTimeSelect': typeof import("element-plus/es/components/time-select/index")['ElTimeSelect']
    'ElTimeline': typeof import("element-plus/es/components/timeline/index")['ElTimeline']
    'ElTimelineItem': typeof import("element-plus/es/components/timeline/index")['ElTimelineItem']
    'ElTooltip': typeof import("element-plus/es/components/tooltip/index")['ElTooltip']
    'ElTooltipV2': typeof import("element-plus/es/components/tooltip-v2/index")['ElTooltipV2']
    'ElTransfer': typeof import("element-plus/es/components/transfer/index")['ElTransfer']
    'ElTree': typeof import("element-plus/es/components/tree/index")['ElTree']
    'ElTreeSelect': typeof import("element-plus/es/components/tree-select/index")['ElTreeSelect']
    'ElTreeV2': typeof import("element-plus/es/components/tree-v2/index")['ElTreeV2']
    'ElUpload': typeof import("element-plus/es/components/upload/index")['ElUpload']
    'ElIconAddLocation': typeof import("@element-plus/icons-vue")['AddLocation']
    'ElIconAim': typeof import("@element-plus/icons-vue")['Aim']
    'ElIconAlarmClock': typeof import("@element-plus/icons-vue")['AlarmClock']
    'ElIconApple': typeof import("@element-plus/icons-vue")['Apple']
    'ElIconArrowDown': typeof import("@element-plus/icons-vue")['ArrowDown']
    'ElIconArrowDownBold': typeof import("@element-plus/icons-vue")['ArrowDownBold']
    'ElIconArrowLeft': typeof import("@element-plus/icons-vue")['ArrowLeft']
    'ElIconArrowLeftBold': typeof import("@element-plus/icons-vue")['ArrowLeftBold']
    'ElIconArrowRight': typeof import("@element-plus/icons-vue")['ArrowRight']
    'ElIconArrowRightBold': typeof import("@element-plus/icons-vue")['ArrowRightBold']
    'ElIconArrowUp': typeof import("@element-plus/icons-vue")['ArrowUp']
    'ElIconArrowUpBold': typeof import("@element-plus/icons-vue")['ArrowUpBold']
    'ElIconAvatar': typeof import("@element-plus/icons-vue")['Avatar']
    'ElIconBack': typeof import("@element-plus/icons-vue")['Back']
    'ElIconBaseball': typeof import("@element-plus/icons-vue")['Baseball']
    'ElIconBasketball': typeof import("@element-plus/icons-vue")['Basketball']
    'ElIconBell': typeof import("@element-plus/icons-vue")['Bell']
    'ElIconBellFilled': typeof import("@element-plus/icons-vue")['BellFilled']
    'ElIconBicycle': typeof import("@element-plus/icons-vue")['Bicycle']
    'ElIconBottom': typeof import("@element-plus/icons-vue")['Bottom']
    'ElIconBottomLeft': typeof import("@element-plus/icons-vue")['BottomLeft']
    'ElIconBottomRight': typeof import("@element-plus/icons-vue")['BottomRight']
    'ElIconBowl': typeof import("@element-plus/icons-vue")['Bowl']
    'ElIconBox': typeof import("@element-plus/icons-vue")['Box']
    'ElIconBriefcase': typeof import("@element-plus/icons-vue")['Briefcase']
    'ElIconBrush': typeof import("@element-plus/icons-vue")['Brush']
    'ElIconBrushFilled': typeof import("@element-plus/icons-vue")['BrushFilled']
    'ElIconBurger': typeof import("@element-plus/icons-vue")['Burger']
    'ElIconCalendar': typeof import("@element-plus/icons-vue")['Calendar']
    'ElIconCamera': typeof import("@element-plus/icons-vue")['Camera']
    'ElIconCameraFilled': typeof import("@element-plus/icons-vue")['CameraFilled']
    'ElIconCaretBottom': typeof import("@element-plus/icons-vue")['CaretBottom']
    'ElIconCaretLeft': typeof import("@element-plus/icons-vue")['CaretLeft']
    'ElIconCaretRight': typeof import("@element-plus/icons-vue")['CaretRight']
    'ElIconCaretTop': typeof import("@element-plus/icons-vue")['CaretTop']
    'ElIconCellphone': typeof import("@element-plus/icons-vue")['Cellphone']
    'ElIconChatDotRound': typeof import("@element-plus/icons-vue")['ChatDotRound']
    'ElIconChatDotSquare': typeof import("@element-plus/icons-vue")['ChatDotSquare']
    'ElIconChatLineRound': typeof import("@element-plus/icons-vue")['ChatLineRound']
    'ElIconChatLineSquare': typeof import("@element-plus/icons-vue")['ChatLineSquare']
    'ElIconChatRound': typeof import("@element-plus/icons-vue")['ChatRound']
    'ElIconChatSquare': typeof import("@element-plus/icons-vue")['ChatSquare']
    'ElIconCheck': typeof import("@element-plus/icons-vue")['Check']
    'ElIconChecked': typeof import("@element-plus/icons-vue")['Checked']
    'ElIconCherry': typeof import("@element-plus/icons-vue")['Cherry']
    'ElIconChicken': typeof import("@element-plus/icons-vue")['Chicken']
    'ElIconChromeFilled': typeof import("@element-plus/icons-vue")['ChromeFilled']
    'ElIconCircleCheck': typeof import("@element-plus/icons-vue")['CircleCheck']
    'ElIconCircleCheckFilled': typeof import("@element-plus/icons-vue")['CircleCheckFilled']
    'ElIconCircleClose': typeof import("@element-plus/icons-vue")['CircleClose']
    'ElIconCircleCloseFilled': typeof import("@element-plus/icons-vue")['CircleCloseFilled']
    'ElIconCirclePlus': typeof import("@element-plus/icons-vue")['CirclePlus']
    'ElIconCirclePlusFilled': typeof import("@element-plus/icons-vue")['CirclePlusFilled']
    'ElIconClock': typeof import("@element-plus/icons-vue")['Clock']
    'ElIconClose': typeof import("@element-plus/icons-vue")['Close']
    'ElIconCloseBold': typeof import("@element-plus/icons-vue")['CloseBold']
    'ElIconCloudy': typeof import("@element-plus/icons-vue")['Cloudy']
    'ElIconCoffee': typeof import("@element-plus/icons-vue")['Coffee']
    'ElIconCoffeeCup': typeof import("@element-plus/icons-vue")['CoffeeCup']
    'ElIconCoin': typeof import("@element-plus/icons-vue")['Coin']
    'ElIconColdDrink': typeof import("@element-plus/icons-vue")['ColdDrink']
    'ElIconCollection': typeof import("@element-plus/icons-vue")['Collection']
    'ElIconCollectionTag': typeof import("@element-plus/icons-vue")['CollectionTag']
    'ElIconComment': typeof import("@element-plus/icons-vue")['Comment']
    'ElIconCompass': typeof import("@element-plus/icons-vue")['Compass']
    'ElIconConnection': typeof import("@element-plus/icons-vue")['Connection']
    'ElIconCoordinate': typeof import("@element-plus/icons-vue")['Coordinate']
    'ElIconCopyDocument': typeof import("@element-plus/icons-vue")['CopyDocument']
    'ElIconCpu': typeof import("@element-plus/icons-vue")['Cpu']
    'ElIconCreditCard': typeof import("@element-plus/icons-vue")['CreditCard']
    'ElIconCrop': typeof import("@element-plus/icons-vue")['Crop']
    'ElIconDArrowLeft': typeof import("@element-plus/icons-vue")['DArrowLeft']
    'ElIconDArrowRight': typeof import("@element-plus/icons-vue")['DArrowRight']
    'ElIconDCaret': typeof import("@element-plus/icons-vue")['DCaret']
    'ElIconDataAnalysis': typeof import("@element-plus/icons-vue")['DataAnalysis']
    'ElIconDataBoard': typeof import("@element-plus/icons-vue")['DataBoard']
    'ElIconDataLine': typeof import("@element-plus/icons-vue")['DataLine']
    'ElIconDelete': typeof import("@element-plus/icons-vue")['Delete']
    'ElIconDeleteFilled': typeof import("@element-plus/icons-vue")['DeleteFilled']
    'ElIconDeleteLocation': typeof import("@element-plus/icons-vue")['DeleteLocation']
    'ElIconDessert': typeof import("@element-plus/icons-vue")['Dessert']
    'ElIconDiscount': typeof import("@element-plus/icons-vue")['Discount']
    'ElIconDish': typeof import("@element-plus/icons-vue")['Dish']
    'ElIconDishDot': typeof import("@element-plus/icons-vue")['DishDot']
    'ElIconDocument': typeof import("@element-plus/icons-vue")['Document']
    'ElIconDocumentAdd': typeof import("@element-plus/icons-vue")['DocumentAdd']
    'ElIconDocumentChecked': typeof import("@element-plus/icons-vue")['DocumentChecked']
    'ElIconDocumentCopy': typeof import("@element-plus/icons-vue")['DocumentCopy']
    'ElIconDocumentDelete': typeof import("@element-plus/icons-vue")['DocumentDelete']
    'ElIconDocumentRemove': typeof import("@element-plus/icons-vue")['DocumentRemove']
    'ElIconDownload': typeof import("@element-plus/icons-vue")['Download']
    'ElIconDrizzling': typeof import("@element-plus/icons-vue")['Drizzling']
    'ElIconEdit': typeof import("@element-plus/icons-vue")['Edit']
    'ElIconEditPen': typeof import("@element-plus/icons-vue")['EditPen']
    'ElIconEleme': typeof import("@element-plus/icons-vue")['Eleme']
    'ElIconElemeFilled': typeof import("@element-plus/icons-vue")['ElemeFilled']
    'ElIconElementPlus': typeof import("@element-plus/icons-vue")['ElementPlus']
    'ElIconExpand': typeof import("@element-plus/icons-vue")['Expand']
    'ElIconFailed': typeof import("@element-plus/icons-vue")['Failed']
    'ElIconFemale': typeof import("@element-plus/icons-vue")['Female']
    'ElIconFiles': typeof import("@element-plus/icons-vue")['Files']
    'ElIconFilm': typeof import("@element-plus/icons-vue")['Film']
    'ElIconFilter': typeof import("@element-plus/icons-vue")['Filter']
    'ElIconFinished': typeof import("@element-plus/icons-vue")['Finished']
    'ElIconFirstAidKit': typeof import("@element-plus/icons-vue")['FirstAidKit']
    'ElIconFlag': typeof import("@element-plus/icons-vue")['Flag']
    'ElIconFold': typeof import("@element-plus/icons-vue")['Fold']
    'ElIconFolder': typeof import("@element-plus/icons-vue")['Folder']
    'ElIconFolderAdd': typeof import("@element-plus/icons-vue")['FolderAdd']
    'ElIconFolderChecked': typeof import("@element-plus/icons-vue")['FolderChecked']
    'ElIconFolderDelete': typeof import("@element-plus/icons-vue")['FolderDelete']
    'ElIconFolderOpened': typeof import("@element-plus/icons-vue")['FolderOpened']
    'ElIconFolderRemove': typeof import("@element-plus/icons-vue")['FolderRemove']
    'ElIconFood': typeof import("@element-plus/icons-vue")['Food']
    'ElIconFootball': typeof import("@element-plus/icons-vue")['Football']
    'ElIconForkSpoon': typeof import("@element-plus/icons-vue")['ForkSpoon']
    'ElIconFries': typeof import("@element-plus/icons-vue")['Fries']
    'ElIconFullScreen': typeof import("@element-plus/icons-vue")['FullScreen']
    'ElIconGoblet': typeof import("@element-plus/icons-vue")['Goblet']
    'ElIconGobletFull': typeof import("@element-plus/icons-vue")['GobletFull']
    'ElIconGobletSquare': typeof import("@element-plus/icons-vue")['GobletSquare']
    'ElIconGobletSquareFull': typeof import("@element-plus/icons-vue")['GobletSquareFull']
    'ElIconGoldMedal': typeof import("@element-plus/icons-vue")['GoldMedal']
    'ElIconGoods': typeof import("@element-plus/icons-vue")['Goods']
    'ElIconGoodsFilled': typeof import("@element-plus/icons-vue")['GoodsFilled']
    'ElIconGrape': typeof import("@element-plus/icons-vue")['Grape']
    'ElIconGrid': typeof import("@element-plus/icons-vue")['Grid']
    'ElIconGuide': typeof import("@element-plus/icons-vue")['Guide']
    'ElIconHandbag': typeof import("@element-plus/icons-vue")['Handbag']
    'ElIconHeadset': typeof import("@element-plus/icons-vue")['Headset']
    'ElIconHelp': typeof import("@element-plus/icons-vue")['Help']
    'ElIconHelpFilled': typeof import("@element-plus/icons-vue")['HelpFilled']
    'ElIconHide': typeof import("@element-plus/icons-vue")['Hide']
    'ElIconHistogram': typeof import("@element-plus/icons-vue")['Histogram']
    'ElIconHomeFilled': typeof import("@element-plus/icons-vue")['HomeFilled']
    'ElIconHotWater': typeof import("@element-plus/icons-vue")['HotWater']
    'ElIconHouse': typeof import("@element-plus/icons-vue")['House']
    'ElIconIceCream': typeof import("@element-plus/icons-vue")['IceCream']
    'ElIconIceCreamRound': typeof import("@element-plus/icons-vue")['IceCreamRound']
    'ElIconIceCreamSquare': typeof import("@element-plus/icons-vue")['IceCreamSquare']
    'ElIconIceDrink': typeof import("@element-plus/icons-vue")['IceDrink']
    'ElIconIceTea': typeof import("@element-plus/icons-vue")['IceTea']
    'ElIconInfoFilled': typeof import("@element-plus/icons-vue")['InfoFilled']
    'ElIconIphone': typeof import("@element-plus/icons-vue")['Iphone']
    'ElIconKey': typeof import("@element-plus/icons-vue")['Key']
    'ElIconKnifeFork': typeof import("@element-plus/icons-vue")['KnifeFork']
    'ElIconLightning': typeof import("@element-plus/icons-vue")['Lightning']
    'ElIconLink': typeof import("@element-plus/icons-vue")['Link']
    'ElIconList': typeof import("@element-plus/icons-vue")['List']
    'ElIconLoading': typeof import("@element-plus/icons-vue")['Loading']
    'ElIconLocation': typeof import("@element-plus/icons-vue")['Location']
    'ElIconLocationFilled': typeof import("@element-plus/icons-vue")['LocationFilled']
    'ElIconLocationInformation': typeof import("@element-plus/icons-vue")['LocationInformation']
    'ElIconLock': typeof import("@element-plus/icons-vue")['Lock']
    'ElIconLollipop': typeof import("@element-plus/icons-vue")['Lollipop']
    'ElIconMagicStick': typeof import("@element-plus/icons-vue")['MagicStick']
    'ElIconMagnet': typeof import("@element-plus/icons-vue")['Magnet']
    'ElIconMale': typeof import("@element-plus/icons-vue")['Male']
    'ElIconManagement': typeof import("@element-plus/icons-vue")['Management']
    'ElIconMapLocation': typeof import("@element-plus/icons-vue")['MapLocation']
    'ElIconMedal': typeof import("@element-plus/icons-vue")['Medal']
    'ElIconMemo': typeof import("@element-plus/icons-vue")['Memo']
    'ElIconMenu': typeof import("@element-plus/icons-vue")['Menu']
    'ElIconMessage': typeof import("@element-plus/icons-vue")['Message']
    'ElIconMessageBox': typeof import("@element-plus/icons-vue")['MessageBox']
    'ElIconMic': typeof import("@element-plus/icons-vue")['Mic']
    'ElIconMicrophone': typeof import("@element-plus/icons-vue")['Microphone']
    'ElIconMilkTea': typeof import("@element-plus/icons-vue")['MilkTea']
    'ElIconMinus': typeof import("@element-plus/icons-vue")['Minus']
    'ElIconMoney': typeof import("@element-plus/icons-vue")['Money']
    'ElIconMonitor': typeof import("@element-plus/icons-vue")['Monitor']
    'ElIconMoon': typeof import("@element-plus/icons-vue")['Moon']
    'ElIconMoonNight': typeof import("@element-plus/icons-vue")['MoonNight']
    'ElIconMore': typeof import("@element-plus/icons-vue")['More']
    'ElIconMoreFilled': typeof import("@element-plus/icons-vue")['MoreFilled']
    'ElIconMostlyCloudy': typeof import("@element-plus/icons-vue")['MostlyCloudy']
    'ElIconMouse': typeof import("@element-plus/icons-vue")['Mouse']
    'ElIconMug': typeof import("@element-plus/icons-vue")['Mug']
    'ElIconMute': typeof import("@element-plus/icons-vue")['Mute']
    'ElIconMuteNotification': typeof import("@element-plus/icons-vue")['MuteNotification']
    'ElIconNoSmoking': typeof import("@element-plus/icons-vue")['NoSmoking']
    'ElIconNotebook': typeof import("@element-plus/icons-vue")['Notebook']
    'ElIconNotification': typeof import("@element-plus/icons-vue")['Notification']
    'ElIconOdometer': typeof import("@element-plus/icons-vue")['Odometer']
    'ElIconOfficeBuilding': typeof import("@element-plus/icons-vue")['OfficeBuilding']
    'ElIconOpen': typeof import("@element-plus/icons-vue")['Open']
    'ElIconOperation': typeof import("@element-plus/icons-vue")['Operation']
    'ElIconOpportunity': typeof import("@element-plus/icons-vue")['Opportunity']
    'ElIconOrange': typeof import("@element-plus/icons-vue")['Orange']
    'ElIconPaperclip': typeof import("@element-plus/icons-vue")['Paperclip']
    'ElIconPartlyCloudy': typeof import("@element-plus/icons-vue")['PartlyCloudy']
    'ElIconPear': typeof import("@element-plus/icons-vue")['Pear']
    'ElIconPhone': typeof import("@element-plus/icons-vue")['Phone']
    'ElIconPhoneFilled': typeof import("@element-plus/icons-vue")['PhoneFilled']
    'ElIconPicture': typeof import("@element-plus/icons-vue")['Picture']
    'ElIconPictureFilled': typeof import("@element-plus/icons-vue")['PictureFilled']
    'ElIconPictureRounded': typeof import("@element-plus/icons-vue")['PictureRounded']
    'ElIconPieChart': typeof import("@element-plus/icons-vue")['PieChart']
    'ElIconPlace': typeof import("@element-plus/icons-vue")['Place']
    'ElIconPlatform': typeof import("@element-plus/icons-vue")['Platform']
    'ElIconPlus': typeof import("@element-plus/icons-vue")['Plus']
    'ElIconPointer': typeof import("@element-plus/icons-vue")['Pointer']
    'ElIconPosition': typeof import("@element-plus/icons-vue")['Position']
    'ElIconPostcard': typeof import("@element-plus/icons-vue")['Postcard']
    'ElIconPouring': typeof import("@element-plus/icons-vue")['Pouring']
    'ElIconPresent': typeof import("@element-plus/icons-vue")['Present']
    'ElIconPriceTag': typeof import("@element-plus/icons-vue")['PriceTag']
    'ElIconPrinter': typeof import("@element-plus/icons-vue")['Printer']
    'ElIconPromotion': typeof import("@element-plus/icons-vue")['Promotion']
    'ElIconQuartzWatch': typeof import("@element-plus/icons-vue")['QuartzWatch']
    'ElIconQuestionFilled': typeof import("@element-plus/icons-vue")['QuestionFilled']
    'ElIconRank': typeof import("@element-plus/icons-vue")['Rank']
    'ElIconReading': typeof import("@element-plus/icons-vue")['Reading']
    'ElIconReadingLamp': typeof import("@element-plus/icons-vue")['ReadingLamp']
    'ElIconRefresh': typeof import("@element-plus/icons-vue")['Refresh']
    'ElIconRefreshLeft': typeof import("@element-plus/icons-vue")['RefreshLeft']
    'ElIconRefreshRight': typeof import("@element-plus/icons-vue")['RefreshRight']
    'ElIconRefrigerator': typeof import("@element-plus/icons-vue")['Refrigerator']
    'ElIconRemove': typeof import("@element-plus/icons-vue")['Remove']
    'ElIconRemoveFilled': typeof import("@element-plus/icons-vue")['RemoveFilled']
    'ElIconRight': typeof import("@element-plus/icons-vue")['Right']
    'ElIconScaleToOriginal': typeof import("@element-plus/icons-vue")['ScaleToOriginal']
    'ElIconSchool': typeof import("@element-plus/icons-vue")['School']
    'ElIconScissor': typeof import("@element-plus/icons-vue")['Scissor']
    'ElIconSearch': typeof import("@element-plus/icons-vue")['Search']
    'ElIconSelect': typeof import("@element-plus/icons-vue")['Select']
    'ElIconSell': typeof import("@element-plus/icons-vue")['Sell']
    'ElIconSemiSelect': typeof import("@element-plus/icons-vue")['SemiSelect']
    'ElIconService': typeof import("@element-plus/icons-vue")['Service']
    'ElIconSetUp': typeof import("@element-plus/icons-vue")['SetUp']
    'ElIconSetting': typeof import("@element-plus/icons-vue")['Setting']
    'ElIconShare': typeof import("@element-plus/icons-vue")['Share']
    'ElIconShip': typeof import("@element-plus/icons-vue")['Ship']
    'ElIconShop': typeof import("@element-plus/icons-vue")['Shop']
    'ElIconShoppingBag': typeof import("@element-plus/icons-vue")['ShoppingBag']
    'ElIconShoppingCart': typeof import("@element-plus/icons-vue")['ShoppingCart']
    'ElIconShoppingCartFull': typeof import("@element-plus/icons-vue")['ShoppingCartFull']
    'ElIconShoppingTrolley': typeof import("@element-plus/icons-vue")['ShoppingTrolley']
    'ElIconSmoking': typeof import("@element-plus/icons-vue")['Smoking']
    'ElIconSoccer': typeof import("@element-plus/icons-vue")['Soccer']
    'ElIconSoldOut': typeof import("@element-plus/icons-vue")['SoldOut']
    'ElIconSort': typeof import("@element-plus/icons-vue")['Sort']
    'ElIconSortDown': typeof import("@element-plus/icons-vue")['SortDown']
    'ElIconSortUp': typeof import("@element-plus/icons-vue")['SortUp']
    'ElIconStamp': typeof import("@element-plus/icons-vue")['Stamp']
    'ElIconStar': typeof import("@element-plus/icons-vue")['Star']
    'ElIconStarFilled': typeof import("@element-plus/icons-vue")['StarFilled']
    'ElIconStopwatch': typeof import("@element-plus/icons-vue")['Stopwatch']
    'ElIconSuccessFilled': typeof import("@element-plus/icons-vue")['SuccessFilled']
    'ElIconSugar': typeof import("@element-plus/icons-vue")['Sugar']
    'ElIconSuitcase': typeof import("@element-plus/icons-vue")['Suitcase']
    'ElIconSuitcaseLine': typeof import("@element-plus/icons-vue")['SuitcaseLine']
    'ElIconSunny': typeof import("@element-plus/icons-vue")['Sunny']
    'ElIconSunrise': typeof import("@element-plus/icons-vue")['Sunrise']
    'ElIconSunset': typeof import("@element-plus/icons-vue")['Sunset']
    'ElIconSwitch': typeof import("@element-plus/icons-vue")['Switch']
    'ElIconSwitchButton': typeof import("@element-plus/icons-vue")['SwitchButton']
    'ElIconSwitchFilled': typeof import("@element-plus/icons-vue")['SwitchFilled']
    'ElIconTakeawayBox': typeof import("@element-plus/icons-vue")['TakeawayBox']
    'ElIconTicket': typeof import("@element-plus/icons-vue")['Ticket']
    'ElIconTickets': typeof import("@element-plus/icons-vue")['Tickets']
    'ElIconTimer': typeof import("@element-plus/icons-vue")['Timer']
    'ElIconToiletPaper': typeof import("@element-plus/icons-vue")['ToiletPaper']
    'ElIconTools': typeof import("@element-plus/icons-vue")['Tools']
    'ElIconTop': typeof import("@element-plus/icons-vue")['Top']
    'ElIconTopLeft': typeof import("@element-plus/icons-vue")['TopLeft']
    'ElIconTopRight': typeof import("@element-plus/icons-vue")['TopRight']
    'ElIconTrendCharts': typeof import("@element-plus/icons-vue")['TrendCharts']
    'ElIconTrophy': typeof import("@element-plus/icons-vue")['Trophy']
    'ElIconTrophyBase': typeof import("@element-plus/icons-vue")['TrophyBase']
    'ElIconTurnOff': typeof import("@element-plus/icons-vue")['TurnOff']
    'ElIconUmbrella': typeof import("@element-plus/icons-vue")['Umbrella']
    'ElIconUnlock': typeof import("@element-plus/icons-vue")['Unlock']
    'ElIconUpload': typeof import("@element-plus/icons-vue")['Upload']
    'ElIconUploadFilled': typeof import("@element-plus/icons-vue")['UploadFilled']
    'ElIconUser': typeof import("@element-plus/icons-vue")['User']
    'ElIconUserFilled': typeof import("@element-plus/icons-vue")['UserFilled']
    'ElIconVan': typeof import("@element-plus/icons-vue")['Van']
    'ElIconVideoCamera': typeof import("@element-plus/icons-vue")['VideoCamera']
    'ElIconVideoCameraFilled': typeof import("@element-plus/icons-vue")['VideoCameraFilled']
    'ElIconVideoPause': typeof import("@element-plus/icons-vue")['VideoPause']
    'ElIconVideoPlay': typeof import("@element-plus/icons-vue")['VideoPlay']
    'ElIconView': typeof import("@element-plus/icons-vue")['View']
    'ElIconWallet': typeof import("@element-plus/icons-vue")['Wallet']
    'ElIconWalletFilled': typeof import("@element-plus/icons-vue")['WalletFilled']
    'ElIconWarnTriangleFilled': typeof import("@element-plus/icons-vue")['WarnTriangleFilled']
    'ElIconWarning': typeof import("@element-plus/icons-vue")['Warning']
    'ElIconWarningFilled': typeof import("@element-plus/icons-vue")['WarningFilled']
    'ElIconWatch': typeof import("@element-plus/icons-vue")['Watch']
    'ElIconWatermelon': typeof import("@element-plus/icons-vue")['Watermelon']
    'ElIconWindPower': typeof import("@element-plus/icons-vue")['WindPower']
    'ElIconZoomIn': typeof import("@element-plus/icons-vue")['ZoomIn']
    'ElIconZoomOut': typeof import("@element-plus/icons-vue")['ZoomOut']
    'NuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'NoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'Link': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'Base': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'Title': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'Meta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'Style': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'Head': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'Html': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'Body': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
    'LazyIcon': typeof import("../components/icon/index.vue")['default']
    'LazyLoginDialog': typeof import("../components/login-dialog/index.vue")['default']
    'LazyLogin': typeof import("../components/login-dialog/login.vue")['default']
    'LazyLoginDialogRegister': typeof import("../components/login-dialog/register.vue")['default']
    'LazySidebar': typeof import("../components/sidebar/index.vue")['default']
    'LazySmsCode': typeof import("../components/sms-code/index.vue")['default']
    'LazyUploadFile': typeof import("../components/upload-file/index.vue")['default']
    'LazyNuxtWelcome': typeof import("../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
    'LazyNuxtLayout': typeof import("../node_modules/nuxt/dist/app/components/layout")['default']
    'LazyNuxtErrorBoundary': typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
    'LazyClientOnly': typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
    'LazyDevOnly': typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
    'LazyServerPlaceholder': typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
    'LazyNuxtLink': typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
    'LazyNuxtLoadingIndicator': typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
    'LazyElAffix': typeof import("element-plus/es/components/affix/index")['ElAffix']
    'LazyElAlert': typeof import("element-plus/es/components/alert/index")['ElAlert']
    'LazyElAutocomplete': typeof import("element-plus/es/components/autocomplete/index")['ElAutocomplete']
    'LazyElAutoResizer': typeof import("element-plus/es/components/table-v2/index")['ElAutoResizer']
    'LazyElAvatar': typeof import("element-plus/es/components/avatar/index")['ElAvatar']
    'LazyElBacktop': typeof import("element-plus/es/components/backtop/index")['ElBacktop']
    'LazyElBadge': typeof import("element-plus/es/components/badge/index")['ElBadge']
    'LazyElBreadcrumb': typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumb']
    'LazyElBreadcrumbItem': typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumbItem']
    'LazyElButton': typeof import("element-plus/es/components/button/index")['ElButton']
    'LazyElButtonGroup': typeof import("element-plus/es/components/button/index")['ElButtonGroup']
    'LazyElCalendar': typeof import("element-plus/es/components/calendar/index")['ElCalendar']
    'LazyElCard': typeof import("element-plus/es/components/card/index")['ElCard']
    'LazyElCarousel': typeof import("element-plus/es/components/carousel/index")['ElCarousel']
    'LazyElCarouselItem': typeof import("element-plus/es/components/carousel/index")['ElCarouselItem']
    'LazyElCascader': typeof import("element-plus/es/components/cascader/index")['ElCascader']
    'LazyElCascaderPanel': typeof import("element-plus/es/components/cascader-panel/index")['ElCascaderPanel']
    'LazyElCheckTag': typeof import("element-plus/es/components/check-tag/index")['ElCheckTag']
    'LazyElCheckbox': typeof import("element-plus/es/components/checkbox/index")['ElCheckbox']
    'LazyElCheckboxButton': typeof import("element-plus/es/components/checkbox/index")['ElCheckboxButton']
    'LazyElCheckboxGroup': typeof import("element-plus/es/components/checkbox/index")['ElCheckboxGroup']
    'LazyElCol': typeof import("element-plus/es/components/col/index")['ElCol']
    'LazyElCollapse': typeof import("element-plus/es/components/collapse/index")['ElCollapse']
    'LazyElCollapseItem': typeof import("element-plus/es/components/collapse/index")['ElCollapseItem']
    'LazyElCollapseTransition': typeof import("element-plus/es/components/collapse-transition/index")['ElCollapseTransition']
    'LazyElColorPicker': typeof import("element-plus/es/components/color-picker/index")['ElColorPicker']
    'LazyElConfigProvider': typeof import("element-plus/es/components/config-provider/index")['ElConfigProvider']
    'LazyElContainer': typeof import("element-plus/es/components/container/index")['ElContainer']
    'LazyElAside': typeof import("element-plus/es/components/container/index")['ElAside']
    'LazyElFooter': typeof import("element-plus/es/components/container/index")['ElFooter']
    'LazyElHeader': typeof import("element-plus/es/components/container/index")['ElHeader']
    'LazyElMain': typeof import("element-plus/es/components/container/index")['ElMain']
    'LazyElDatePicker': typeof import("element-plus/es/components/date-picker/index")['ElDatePicker']
    'LazyElDescriptions': typeof import("element-plus/es/components/descriptions/index")['ElDescriptions']
    'LazyElDescriptionsItem': typeof import("element-plus/es/components/descriptions/index")['ElDescriptionsItem']
    'LazyElDialog': typeof import("element-plus/es/components/dialog/index")['ElDialog']
    'LazyElDivider': typeof import("element-plus/es/components/divider/index")['ElDivider']
    'LazyElDrawer': typeof import("element-plus/es/components/drawer/index")['ElDrawer']
    'LazyElDropdown': typeof import("element-plus/es/components/dropdown/index")['ElDropdown']
    'LazyElDropdownItem': typeof import("element-plus/es/components/dropdown/index")['ElDropdownItem']
    'LazyElDropdownMenu': typeof import("element-plus/es/components/dropdown/index")['ElDropdownMenu']
    'LazyElEmpty': typeof import("element-plus/es/components/empty/index")['ElEmpty']
    'LazyElForm': typeof import("element-plus/es/components/form/index")['ElForm']
    'LazyElFormItem': typeof import("element-plus/es/components/form/index")['ElFormItem']
    'LazyElIcon': typeof import("element-plus/es/components/icon/index")['ElIcon']
    'LazyElImage': typeof import("element-plus/es/components/image/index")['ElImage']
    'LazyElImageViewer': typeof import("element-plus/es/components/image-viewer/index")['ElImageViewer']
    'LazyElInput': typeof import("element-plus/es/components/input/index")['ElInput']
    'LazyElInputNumber': typeof import("element-plus/es/components/input-number/index")['ElInputNumber']
    'LazyElLink': typeof import("element-plus/es/components/link/index")['ElLink']
    'LazyElMenu': typeof import("element-plus/es/components/menu/index")['ElMenu']
    'LazyElMenuItem': typeof import("element-plus/es/components/menu/index")['ElMenuItem']
    'LazyElMenuItemGroup': typeof import("element-plus/es/components/menu/index")['ElMenuItemGroup']
    'LazyElSubMenu': typeof import("element-plus/es/components/menu/index")['ElSubMenu']
    'LazyElPageHeader': typeof import("element-plus/es/components/page-header/index")['ElPageHeader']
    'LazyElPagination': typeof import("element-plus/es/components/pagination/index")['ElPagination']
    'LazyElPopconfirm': typeof import("element-plus/es/components/popconfirm/index")['ElPopconfirm']
    'LazyElPopover': typeof import("element-plus/es/components/popover/index")['ElPopover']
    'LazyElPopper': typeof import("element-plus/es/components/popper/index")['ElPopper']
    'LazyElProgress': typeof import("element-plus/es/components/progress/index")['ElProgress']
    'LazyElRadio': typeof import("element-plus/es/components/radio/index")['ElRadio']
    'LazyElRadioButton': typeof import("element-plus/es/components/radio/index")['ElRadioButton']
    'LazyElRadioGroup': typeof import("element-plus/es/components/radio/index")['ElRadioGroup']
    'LazyElRate': typeof import("element-plus/es/components/rate/index")['ElRate']
    'LazyElResult': typeof import("element-plus/es/components/result/index")['ElResult']
    'LazyElRow': typeof import("element-plus/es/components/row/index")['ElRow']
    'LazyElScrollbar': typeof import("element-plus/es/components/scrollbar/index")['ElScrollbar']
    'LazyElSelect': typeof import("element-plus/es/components/select/index")['ElSelect']
    'LazyElOption': typeof import("element-plus/es/components/select/index")['ElOption']
    'LazyElOptionGroup': typeof import("element-plus/es/components/select/index")['ElOptionGroup']
    'LazyElSelectV2': typeof import("element-plus/es/components/select-v2/index")['ElSelectV2']
    'LazyElSkeleton': typeof import("element-plus/es/components/skeleton/index")['ElSkeleton']
    'LazyElSkeletonItem': typeof import("element-plus/es/components/skeleton/index")['ElSkeletonItem']
    'LazyElSlider': typeof import("element-plus/es/components/slider/index")['ElSlider']
    'LazyElSpace': typeof import("element-plus/es/components/space/index")['ElSpace']
    'LazyElStatistic': typeof import("element-plus/es/components/statistic/index")['ElStatistic']
    'LazyElCountdown': typeof import("element-plus/es/components/countdown/index")['ElCountdown']
    'LazyElSteps': typeof import("element-plus/es/components/steps/index")['ElSteps']
    'LazyElStep': typeof import("element-plus/es/components/steps/index")['ElStep']
    'LazyElSwitch': typeof import("element-plus/es/components/switch/index")['ElSwitch']
    'LazyElTable': typeof import("element-plus/es/components/table/index")['ElTable']
    'LazyElTableColumn': typeof import("element-plus/es/components/table/index")['ElTableColumn']
    'LazyElTableV2': typeof import("element-plus/es/components/table-v2/index")['ElTableV2']
    'LazyElTabs': typeof import("element-plus/es/components/tabs/index")['ElTabs']
    'LazyElTabPane': typeof import("element-plus/es/components/tabs/index")['ElTabPane']
    'LazyElTag': typeof import("element-plus/es/components/tag/index")['ElTag']
    'LazyElText': typeof import("element-plus/es/components/text/index")['ElText']
    'LazyElTimePicker': typeof import("element-plus/es/components/time-picker/index")['ElTimePicker']
    'LazyElTimeSelect': typeof import("element-plus/es/components/time-select/index")['ElTimeSelect']
    'LazyElTimeline': typeof import("element-plus/es/components/timeline/index")['ElTimeline']
    'LazyElTimelineItem': typeof import("element-plus/es/components/timeline/index")['ElTimelineItem']
    'LazyElTooltip': typeof import("element-plus/es/components/tooltip/index")['ElTooltip']
    'LazyElTooltipV2': typeof import("element-plus/es/components/tooltip-v2/index")['ElTooltipV2']
    'LazyElTransfer': typeof import("element-plus/es/components/transfer/index")['ElTransfer']
    'LazyElTree': typeof import("element-plus/es/components/tree/index")['ElTree']
    'LazyElTreeSelect': typeof import("element-plus/es/components/tree-select/index")['ElTreeSelect']
    'LazyElTreeV2': typeof import("element-plus/es/components/tree-v2/index")['ElTreeV2']
    'LazyElUpload': typeof import("element-plus/es/components/upload/index")['ElUpload']
    'LazyElIconAddLocation': typeof import("@element-plus/icons-vue")['AddLocation']
    'LazyElIconAim': typeof import("@element-plus/icons-vue")['Aim']
    'LazyElIconAlarmClock': typeof import("@element-plus/icons-vue")['AlarmClock']
    'LazyElIconApple': typeof import("@element-plus/icons-vue")['Apple']
    'LazyElIconArrowDown': typeof import("@element-plus/icons-vue")['ArrowDown']
    'LazyElIconArrowDownBold': typeof import("@element-plus/icons-vue")['ArrowDownBold']
    'LazyElIconArrowLeft': typeof import("@element-plus/icons-vue")['ArrowLeft']
    'LazyElIconArrowLeftBold': typeof import("@element-plus/icons-vue")['ArrowLeftBold']
    'LazyElIconArrowRight': typeof import("@element-plus/icons-vue")['ArrowRight']
    'LazyElIconArrowRightBold': typeof import("@element-plus/icons-vue")['ArrowRightBold']
    'LazyElIconArrowUp': typeof import("@element-plus/icons-vue")['ArrowUp']
    'LazyElIconArrowUpBold': typeof import("@element-plus/icons-vue")['ArrowUpBold']
    'LazyElIconAvatar': typeof import("@element-plus/icons-vue")['Avatar']
    'LazyElIconBack': typeof import("@element-plus/icons-vue")['Back']
    'LazyElIconBaseball': typeof import("@element-plus/icons-vue")['Baseball']
    'LazyElIconBasketball': typeof import("@element-plus/icons-vue")['Basketball']
    'LazyElIconBell': typeof import("@element-plus/icons-vue")['Bell']
    'LazyElIconBellFilled': typeof import("@element-plus/icons-vue")['BellFilled']
    'LazyElIconBicycle': typeof import("@element-plus/icons-vue")['Bicycle']
    'LazyElIconBottom': typeof import("@element-plus/icons-vue")['Bottom']
    'LazyElIconBottomLeft': typeof import("@element-plus/icons-vue")['BottomLeft']
    'LazyElIconBottomRight': typeof import("@element-plus/icons-vue")['BottomRight']
    'LazyElIconBowl': typeof import("@element-plus/icons-vue")['Bowl']
    'LazyElIconBox': typeof import("@element-plus/icons-vue")['Box']
    'LazyElIconBriefcase': typeof import("@element-plus/icons-vue")['Briefcase']
    'LazyElIconBrush': typeof import("@element-plus/icons-vue")['Brush']
    'LazyElIconBrushFilled': typeof import("@element-plus/icons-vue")['BrushFilled']
    'LazyElIconBurger': typeof import("@element-plus/icons-vue")['Burger']
    'LazyElIconCalendar': typeof import("@element-plus/icons-vue")['Calendar']
    'LazyElIconCamera': typeof import("@element-plus/icons-vue")['Camera']
    'LazyElIconCameraFilled': typeof import("@element-plus/icons-vue")['CameraFilled']
    'LazyElIconCaretBottom': typeof import("@element-plus/icons-vue")['CaretBottom']
    'LazyElIconCaretLeft': typeof import("@element-plus/icons-vue")['CaretLeft']
    'LazyElIconCaretRight': typeof import("@element-plus/icons-vue")['CaretRight']
    'LazyElIconCaretTop': typeof import("@element-plus/icons-vue")['CaretTop']
    'LazyElIconCellphone': typeof import("@element-plus/icons-vue")['Cellphone']
    'LazyElIconChatDotRound': typeof import("@element-plus/icons-vue")['ChatDotRound']
    'LazyElIconChatDotSquare': typeof import("@element-plus/icons-vue")['ChatDotSquare']
    'LazyElIconChatLineRound': typeof import("@element-plus/icons-vue")['ChatLineRound']
    'LazyElIconChatLineSquare': typeof import("@element-plus/icons-vue")['ChatLineSquare']
    'LazyElIconChatRound': typeof import("@element-plus/icons-vue")['ChatRound']
    'LazyElIconChatSquare': typeof import("@element-plus/icons-vue")['ChatSquare']
    'LazyElIconCheck': typeof import("@element-plus/icons-vue")['Check']
    'LazyElIconChecked': typeof import("@element-plus/icons-vue")['Checked']
    'LazyElIconCherry': typeof import("@element-plus/icons-vue")['Cherry']
    'LazyElIconChicken': typeof import("@element-plus/icons-vue")['Chicken']
    'LazyElIconChromeFilled': typeof import("@element-plus/icons-vue")['ChromeFilled']
    'LazyElIconCircleCheck': typeof import("@element-plus/icons-vue")['CircleCheck']
    'LazyElIconCircleCheckFilled': typeof import("@element-plus/icons-vue")['CircleCheckFilled']
    'LazyElIconCircleClose': typeof import("@element-plus/icons-vue")['CircleClose']
    'LazyElIconCircleCloseFilled': typeof import("@element-plus/icons-vue")['CircleCloseFilled']
    'LazyElIconCirclePlus': typeof import("@element-plus/icons-vue")['CirclePlus']
    'LazyElIconCirclePlusFilled': typeof import("@element-plus/icons-vue")['CirclePlusFilled']
    'LazyElIconClock': typeof import("@element-plus/icons-vue")['Clock']
    'LazyElIconClose': typeof import("@element-plus/icons-vue")['Close']
    'LazyElIconCloseBold': typeof import("@element-plus/icons-vue")['CloseBold']
    'LazyElIconCloudy': typeof import("@element-plus/icons-vue")['Cloudy']
    'LazyElIconCoffee': typeof import("@element-plus/icons-vue")['Coffee']
    'LazyElIconCoffeeCup': typeof import("@element-plus/icons-vue")['CoffeeCup']
    'LazyElIconCoin': typeof import("@element-plus/icons-vue")['Coin']
    'LazyElIconColdDrink': typeof import("@element-plus/icons-vue")['ColdDrink']
    'LazyElIconCollection': typeof import("@element-plus/icons-vue")['Collection']
    'LazyElIconCollectionTag': typeof import("@element-plus/icons-vue")['CollectionTag']
    'LazyElIconComment': typeof import("@element-plus/icons-vue")['Comment']
    'LazyElIconCompass': typeof import("@element-plus/icons-vue")['Compass']
    'LazyElIconConnection': typeof import("@element-plus/icons-vue")['Connection']
    'LazyElIconCoordinate': typeof import("@element-plus/icons-vue")['Coordinate']
    'LazyElIconCopyDocument': typeof import("@element-plus/icons-vue")['CopyDocument']
    'LazyElIconCpu': typeof import("@element-plus/icons-vue")['Cpu']
    'LazyElIconCreditCard': typeof import("@element-plus/icons-vue")['CreditCard']
    'LazyElIconCrop': typeof import("@element-plus/icons-vue")['Crop']
    'LazyElIconDArrowLeft': typeof import("@element-plus/icons-vue")['DArrowLeft']
    'LazyElIconDArrowRight': typeof import("@element-plus/icons-vue")['DArrowRight']
    'LazyElIconDCaret': typeof import("@element-plus/icons-vue")['DCaret']
    'LazyElIconDataAnalysis': typeof import("@element-plus/icons-vue")['DataAnalysis']
    'LazyElIconDataBoard': typeof import("@element-plus/icons-vue")['DataBoard']
    'LazyElIconDataLine': typeof import("@element-plus/icons-vue")['DataLine']
    'LazyElIconDelete': typeof import("@element-plus/icons-vue")['Delete']
    'LazyElIconDeleteFilled': typeof import("@element-plus/icons-vue")['DeleteFilled']
    'LazyElIconDeleteLocation': typeof import("@element-plus/icons-vue")['DeleteLocation']
    'LazyElIconDessert': typeof import("@element-plus/icons-vue")['Dessert']
    'LazyElIconDiscount': typeof import("@element-plus/icons-vue")['Discount']
    'LazyElIconDish': typeof import("@element-plus/icons-vue")['Dish']
    'LazyElIconDishDot': typeof import("@element-plus/icons-vue")['DishDot']
    'LazyElIconDocument': typeof import("@element-plus/icons-vue")['Document']
    'LazyElIconDocumentAdd': typeof import("@element-plus/icons-vue")['DocumentAdd']
    'LazyElIconDocumentChecked': typeof import("@element-plus/icons-vue")['DocumentChecked']
    'LazyElIconDocumentCopy': typeof import("@element-plus/icons-vue")['DocumentCopy']
    'LazyElIconDocumentDelete': typeof import("@element-plus/icons-vue")['DocumentDelete']
    'LazyElIconDocumentRemove': typeof import("@element-plus/icons-vue")['DocumentRemove']
    'LazyElIconDownload': typeof import("@element-plus/icons-vue")['Download']
    'LazyElIconDrizzling': typeof import("@element-plus/icons-vue")['Drizzling']
    'LazyElIconEdit': typeof import("@element-plus/icons-vue")['Edit']
    'LazyElIconEditPen': typeof import("@element-plus/icons-vue")['EditPen']
    'LazyElIconEleme': typeof import("@element-plus/icons-vue")['Eleme']
    'LazyElIconElemeFilled': typeof import("@element-plus/icons-vue")['ElemeFilled']
    'LazyElIconElementPlus': typeof import("@element-plus/icons-vue")['ElementPlus']
    'LazyElIconExpand': typeof import("@element-plus/icons-vue")['Expand']
    'LazyElIconFailed': typeof import("@element-plus/icons-vue")['Failed']
    'LazyElIconFemale': typeof import("@element-plus/icons-vue")['Female']
    'LazyElIconFiles': typeof import("@element-plus/icons-vue")['Files']
    'LazyElIconFilm': typeof import("@element-plus/icons-vue")['Film']
    'LazyElIconFilter': typeof import("@element-plus/icons-vue")['Filter']
    'LazyElIconFinished': typeof import("@element-plus/icons-vue")['Finished']
    'LazyElIconFirstAidKit': typeof import("@element-plus/icons-vue")['FirstAidKit']
    'LazyElIconFlag': typeof import("@element-plus/icons-vue")['Flag']
    'LazyElIconFold': typeof import("@element-plus/icons-vue")['Fold']
    'LazyElIconFolder': typeof import("@element-plus/icons-vue")['Folder']
    'LazyElIconFolderAdd': typeof import("@element-plus/icons-vue")['FolderAdd']
    'LazyElIconFolderChecked': typeof import("@element-plus/icons-vue")['FolderChecked']
    'LazyElIconFolderDelete': typeof import("@element-plus/icons-vue")['FolderDelete']
    'LazyElIconFolderOpened': typeof import("@element-plus/icons-vue")['FolderOpened']
    'LazyElIconFolderRemove': typeof import("@element-plus/icons-vue")['FolderRemove']
    'LazyElIconFood': typeof import("@element-plus/icons-vue")['Food']
    'LazyElIconFootball': typeof import("@element-plus/icons-vue")['Football']
    'LazyElIconForkSpoon': typeof import("@element-plus/icons-vue")['ForkSpoon']
    'LazyElIconFries': typeof import("@element-plus/icons-vue")['Fries']
    'LazyElIconFullScreen': typeof import("@element-plus/icons-vue")['FullScreen']
    'LazyElIconGoblet': typeof import("@element-plus/icons-vue")['Goblet']
    'LazyElIconGobletFull': typeof import("@element-plus/icons-vue")['GobletFull']
    'LazyElIconGobletSquare': typeof import("@element-plus/icons-vue")['GobletSquare']
    'LazyElIconGobletSquareFull': typeof import("@element-plus/icons-vue")['GobletSquareFull']
    'LazyElIconGoldMedal': typeof import("@element-plus/icons-vue")['GoldMedal']
    'LazyElIconGoods': typeof import("@element-plus/icons-vue")['Goods']
    'LazyElIconGoodsFilled': typeof import("@element-plus/icons-vue")['GoodsFilled']
    'LazyElIconGrape': typeof import("@element-plus/icons-vue")['Grape']
    'LazyElIconGrid': typeof import("@element-plus/icons-vue")['Grid']
    'LazyElIconGuide': typeof import("@element-plus/icons-vue")['Guide']
    'LazyElIconHandbag': typeof import("@element-plus/icons-vue")['Handbag']
    'LazyElIconHeadset': typeof import("@element-plus/icons-vue")['Headset']
    'LazyElIconHelp': typeof import("@element-plus/icons-vue")['Help']
    'LazyElIconHelpFilled': typeof import("@element-plus/icons-vue")['HelpFilled']
    'LazyElIconHide': typeof import("@element-plus/icons-vue")['Hide']
    'LazyElIconHistogram': typeof import("@element-plus/icons-vue")['Histogram']
    'LazyElIconHomeFilled': typeof import("@element-plus/icons-vue")['HomeFilled']
    'LazyElIconHotWater': typeof import("@element-plus/icons-vue")['HotWater']
    'LazyElIconHouse': typeof import("@element-plus/icons-vue")['House']
    'LazyElIconIceCream': typeof import("@element-plus/icons-vue")['IceCream']
    'LazyElIconIceCreamRound': typeof import("@element-plus/icons-vue")['IceCreamRound']
    'LazyElIconIceCreamSquare': typeof import("@element-plus/icons-vue")['IceCreamSquare']
    'LazyElIconIceDrink': typeof import("@element-plus/icons-vue")['IceDrink']
    'LazyElIconIceTea': typeof import("@element-plus/icons-vue")['IceTea']
    'LazyElIconInfoFilled': typeof import("@element-plus/icons-vue")['InfoFilled']
    'LazyElIconIphone': typeof import("@element-plus/icons-vue")['Iphone']
    'LazyElIconKey': typeof import("@element-plus/icons-vue")['Key']
    'LazyElIconKnifeFork': typeof import("@element-plus/icons-vue")['KnifeFork']
    'LazyElIconLightning': typeof import("@element-plus/icons-vue")['Lightning']
    'LazyElIconLink': typeof import("@element-plus/icons-vue")['Link']
    'LazyElIconList': typeof import("@element-plus/icons-vue")['List']
    'LazyElIconLoading': typeof import("@element-plus/icons-vue")['Loading']
    'LazyElIconLocation': typeof import("@element-plus/icons-vue")['Location']
    'LazyElIconLocationFilled': typeof import("@element-plus/icons-vue")['LocationFilled']
    'LazyElIconLocationInformation': typeof import("@element-plus/icons-vue")['LocationInformation']
    'LazyElIconLock': typeof import("@element-plus/icons-vue")['Lock']
    'LazyElIconLollipop': typeof import("@element-plus/icons-vue")['Lollipop']
    'LazyElIconMagicStick': typeof import("@element-plus/icons-vue")['MagicStick']
    'LazyElIconMagnet': typeof import("@element-plus/icons-vue")['Magnet']
    'LazyElIconMale': typeof import("@element-plus/icons-vue")['Male']
    'LazyElIconManagement': typeof import("@element-plus/icons-vue")['Management']
    'LazyElIconMapLocation': typeof import("@element-plus/icons-vue")['MapLocation']
    'LazyElIconMedal': typeof import("@element-plus/icons-vue")['Medal']
    'LazyElIconMemo': typeof import("@element-plus/icons-vue")['Memo']
    'LazyElIconMenu': typeof import("@element-plus/icons-vue")['Menu']
    'LazyElIconMessage': typeof import("@element-plus/icons-vue")['Message']
    'LazyElIconMessageBox': typeof import("@element-plus/icons-vue")['MessageBox']
    'LazyElIconMic': typeof import("@element-plus/icons-vue")['Mic']
    'LazyElIconMicrophone': typeof import("@element-plus/icons-vue")['Microphone']
    'LazyElIconMilkTea': typeof import("@element-plus/icons-vue")['MilkTea']
    'LazyElIconMinus': typeof import("@element-plus/icons-vue")['Minus']
    'LazyElIconMoney': typeof import("@element-plus/icons-vue")['Money']
    'LazyElIconMonitor': typeof import("@element-plus/icons-vue")['Monitor']
    'LazyElIconMoon': typeof import("@element-plus/icons-vue")['Moon']
    'LazyElIconMoonNight': typeof import("@element-plus/icons-vue")['MoonNight']
    'LazyElIconMore': typeof import("@element-plus/icons-vue")['More']
    'LazyElIconMoreFilled': typeof import("@element-plus/icons-vue")['MoreFilled']
    'LazyElIconMostlyCloudy': typeof import("@element-plus/icons-vue")['MostlyCloudy']
    'LazyElIconMouse': typeof import("@element-plus/icons-vue")['Mouse']
    'LazyElIconMug': typeof import("@element-plus/icons-vue")['Mug']
    'LazyElIconMute': typeof import("@element-plus/icons-vue")['Mute']
    'LazyElIconMuteNotification': typeof import("@element-plus/icons-vue")['MuteNotification']
    'LazyElIconNoSmoking': typeof import("@element-plus/icons-vue")['NoSmoking']
    'LazyElIconNotebook': typeof import("@element-plus/icons-vue")['Notebook']
    'LazyElIconNotification': typeof import("@element-plus/icons-vue")['Notification']
    'LazyElIconOdometer': typeof import("@element-plus/icons-vue")['Odometer']
    'LazyElIconOfficeBuilding': typeof import("@element-plus/icons-vue")['OfficeBuilding']
    'LazyElIconOpen': typeof import("@element-plus/icons-vue")['Open']
    'LazyElIconOperation': typeof import("@element-plus/icons-vue")['Operation']
    'LazyElIconOpportunity': typeof import("@element-plus/icons-vue")['Opportunity']
    'LazyElIconOrange': typeof import("@element-plus/icons-vue")['Orange']
    'LazyElIconPaperclip': typeof import("@element-plus/icons-vue")['Paperclip']
    'LazyElIconPartlyCloudy': typeof import("@element-plus/icons-vue")['PartlyCloudy']
    'LazyElIconPear': typeof import("@element-plus/icons-vue")['Pear']
    'LazyElIconPhone': typeof import("@element-plus/icons-vue")['Phone']
    'LazyElIconPhoneFilled': typeof import("@element-plus/icons-vue")['PhoneFilled']
    'LazyElIconPicture': typeof import("@element-plus/icons-vue")['Picture']
    'LazyElIconPictureFilled': typeof import("@element-plus/icons-vue")['PictureFilled']
    'LazyElIconPictureRounded': typeof import("@element-plus/icons-vue")['PictureRounded']
    'LazyElIconPieChart': typeof import("@element-plus/icons-vue")['PieChart']
    'LazyElIconPlace': typeof import("@element-plus/icons-vue")['Place']
    'LazyElIconPlatform': typeof import("@element-plus/icons-vue")['Platform']
    'LazyElIconPlus': typeof import("@element-plus/icons-vue")['Plus']
    'LazyElIconPointer': typeof import("@element-plus/icons-vue")['Pointer']
    'LazyElIconPosition': typeof import("@element-plus/icons-vue")['Position']
    'LazyElIconPostcard': typeof import("@element-plus/icons-vue")['Postcard']
    'LazyElIconPouring': typeof import("@element-plus/icons-vue")['Pouring']
    'LazyElIconPresent': typeof import("@element-plus/icons-vue")['Present']
    'LazyElIconPriceTag': typeof import("@element-plus/icons-vue")['PriceTag']
    'LazyElIconPrinter': typeof import("@element-plus/icons-vue")['Printer']
    'LazyElIconPromotion': typeof import("@element-plus/icons-vue")['Promotion']
    'LazyElIconQuartzWatch': typeof import("@element-plus/icons-vue")['QuartzWatch']
    'LazyElIconQuestionFilled': typeof import("@element-plus/icons-vue")['QuestionFilled']
    'LazyElIconRank': typeof import("@element-plus/icons-vue")['Rank']
    'LazyElIconReading': typeof import("@element-plus/icons-vue")['Reading']
    'LazyElIconReadingLamp': typeof import("@element-plus/icons-vue")['ReadingLamp']
    'LazyElIconRefresh': typeof import("@element-plus/icons-vue")['Refresh']
    'LazyElIconRefreshLeft': typeof import("@element-plus/icons-vue")['RefreshLeft']
    'LazyElIconRefreshRight': typeof import("@element-plus/icons-vue")['RefreshRight']
    'LazyElIconRefrigerator': typeof import("@element-plus/icons-vue")['Refrigerator']
    'LazyElIconRemove': typeof import("@element-plus/icons-vue")['Remove']
    'LazyElIconRemoveFilled': typeof import("@element-plus/icons-vue")['RemoveFilled']
    'LazyElIconRight': typeof import("@element-plus/icons-vue")['Right']
    'LazyElIconScaleToOriginal': typeof import("@element-plus/icons-vue")['ScaleToOriginal']
    'LazyElIconSchool': typeof import("@element-plus/icons-vue")['School']
    'LazyElIconScissor': typeof import("@element-plus/icons-vue")['Scissor']
    'LazyElIconSearch': typeof import("@element-plus/icons-vue")['Search']
    'LazyElIconSelect': typeof import("@element-plus/icons-vue")['Select']
    'LazyElIconSell': typeof import("@element-plus/icons-vue")['Sell']
    'LazyElIconSemiSelect': typeof import("@element-plus/icons-vue")['SemiSelect']
    'LazyElIconService': typeof import("@element-plus/icons-vue")['Service']
    'LazyElIconSetUp': typeof import("@element-plus/icons-vue")['SetUp']
    'LazyElIconSetting': typeof import("@element-plus/icons-vue")['Setting']
    'LazyElIconShare': typeof import("@element-plus/icons-vue")['Share']
    'LazyElIconShip': typeof import("@element-plus/icons-vue")['Ship']
    'LazyElIconShop': typeof import("@element-plus/icons-vue")['Shop']
    'LazyElIconShoppingBag': typeof import("@element-plus/icons-vue")['ShoppingBag']
    'LazyElIconShoppingCart': typeof import("@element-plus/icons-vue")['ShoppingCart']
    'LazyElIconShoppingCartFull': typeof import("@element-plus/icons-vue")['ShoppingCartFull']
    'LazyElIconShoppingTrolley': typeof import("@element-plus/icons-vue")['ShoppingTrolley']
    'LazyElIconSmoking': typeof import("@element-plus/icons-vue")['Smoking']
    'LazyElIconSoccer': typeof import("@element-plus/icons-vue")['Soccer']
    'LazyElIconSoldOut': typeof import("@element-plus/icons-vue")['SoldOut']
    'LazyElIconSort': typeof import("@element-plus/icons-vue")['Sort']
    'LazyElIconSortDown': typeof import("@element-plus/icons-vue")['SortDown']
    'LazyElIconSortUp': typeof import("@element-plus/icons-vue")['SortUp']
    'LazyElIconStamp': typeof import("@element-plus/icons-vue")['Stamp']
    'LazyElIconStar': typeof import("@element-plus/icons-vue")['Star']
    'LazyElIconStarFilled': typeof import("@element-plus/icons-vue")['StarFilled']
    'LazyElIconStopwatch': typeof import("@element-plus/icons-vue")['Stopwatch']
    'LazyElIconSuccessFilled': typeof import("@element-plus/icons-vue")['SuccessFilled']
    'LazyElIconSugar': typeof import("@element-plus/icons-vue")['Sugar']
    'LazyElIconSuitcase': typeof import("@element-plus/icons-vue")['Suitcase']
    'LazyElIconSuitcaseLine': typeof import("@element-plus/icons-vue")['SuitcaseLine']
    'LazyElIconSunny': typeof import("@element-plus/icons-vue")['Sunny']
    'LazyElIconSunrise': typeof import("@element-plus/icons-vue")['Sunrise']
    'LazyElIconSunset': typeof import("@element-plus/icons-vue")['Sunset']
    'LazyElIconSwitch': typeof import("@element-plus/icons-vue")['Switch']
    'LazyElIconSwitchButton': typeof import("@element-plus/icons-vue")['SwitchButton']
    'LazyElIconSwitchFilled': typeof import("@element-plus/icons-vue")['SwitchFilled']
    'LazyElIconTakeawayBox': typeof import("@element-plus/icons-vue")['TakeawayBox']
    'LazyElIconTicket': typeof import("@element-plus/icons-vue")['Ticket']
    'LazyElIconTickets': typeof import("@element-plus/icons-vue")['Tickets']
    'LazyElIconTimer': typeof import("@element-plus/icons-vue")['Timer']
    'LazyElIconToiletPaper': typeof import("@element-plus/icons-vue")['ToiletPaper']
    'LazyElIconTools': typeof import("@element-plus/icons-vue")['Tools']
    'LazyElIconTop': typeof import("@element-plus/icons-vue")['Top']
    'LazyElIconTopLeft': typeof import("@element-plus/icons-vue")['TopLeft']
    'LazyElIconTopRight': typeof import("@element-plus/icons-vue")['TopRight']
    'LazyElIconTrendCharts': typeof import("@element-plus/icons-vue")['TrendCharts']
    'LazyElIconTrophy': typeof import("@element-plus/icons-vue")['Trophy']
    'LazyElIconTrophyBase': typeof import("@element-plus/icons-vue")['TrophyBase']
    'LazyElIconTurnOff': typeof import("@element-plus/icons-vue")['TurnOff']
    'LazyElIconUmbrella': typeof import("@element-plus/icons-vue")['Umbrella']
    'LazyElIconUnlock': typeof import("@element-plus/icons-vue")['Unlock']
    'LazyElIconUpload': typeof import("@element-plus/icons-vue")['Upload']
    'LazyElIconUploadFilled': typeof import("@element-plus/icons-vue")['UploadFilled']
    'LazyElIconUser': typeof import("@element-plus/icons-vue")['User']
    'LazyElIconUserFilled': typeof import("@element-plus/icons-vue")['UserFilled']
    'LazyElIconVan': typeof import("@element-plus/icons-vue")['Van']
    'LazyElIconVideoCamera': typeof import("@element-plus/icons-vue")['VideoCamera']
    'LazyElIconVideoCameraFilled': typeof import("@element-plus/icons-vue")['VideoCameraFilled']
    'LazyElIconVideoPause': typeof import("@element-plus/icons-vue")['VideoPause']
    'LazyElIconVideoPlay': typeof import("@element-plus/icons-vue")['VideoPlay']
    'LazyElIconView': typeof import("@element-plus/icons-vue")['View']
    'LazyElIconWallet': typeof import("@element-plus/icons-vue")['Wallet']
    'LazyElIconWalletFilled': typeof import("@element-plus/icons-vue")['WalletFilled']
    'LazyElIconWarnTriangleFilled': typeof import("@element-plus/icons-vue")['WarnTriangleFilled']
    'LazyElIconWarning': typeof import("@element-plus/icons-vue")['Warning']
    'LazyElIconWarningFilled': typeof import("@element-plus/icons-vue")['WarningFilled']
    'LazyElIconWatch': typeof import("@element-plus/icons-vue")['Watch']
    'LazyElIconWatermelon': typeof import("@element-plus/icons-vue")['Watermelon']
    'LazyElIconWindPower': typeof import("@element-plus/icons-vue")['WindPower']
    'LazyElIconZoomIn': typeof import("@element-plus/icons-vue")['ZoomIn']
    'LazyElIconZoomOut': typeof import("@element-plus/icons-vue")['ZoomOut']
    'LazyNuxtPage': typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
    'LazyNoScript': typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
    'LazyLink': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
    'LazyBase': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
    'LazyTitle': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
    'LazyMeta': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
    'LazyStyle': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
    'LazyHead': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
    'LazyHtml': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
    'LazyBody': typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
  }
}

export const Icon: typeof import("../components/icon/index.vue")['default']
export const LoginDialog: typeof import("../components/login-dialog/index.vue")['default']
export const Login: typeof import("../components/login-dialog/login.vue")['default']
export const LoginDialogRegister: typeof import("../components/login-dialog/register.vue")['default']
export const Sidebar: typeof import("../components/sidebar/index.vue")['default']
export const SmsCode: typeof import("../components/sms-code/index.vue")['default']
export const UploadFile: typeof import("../components/upload-file/index.vue")['default']
export const NuxtWelcome: typeof import("../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
export const NuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/layout")['default']
export const NuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const ClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const DevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const ServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const NuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const NuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const ElAffix: typeof import("element-plus/es/components/affix/index")['ElAffix']
export const ElAlert: typeof import("element-plus/es/components/alert/index")['ElAlert']
export const ElAutocomplete: typeof import("element-plus/es/components/autocomplete/index")['ElAutocomplete']
export const ElAutoResizer: typeof import("element-plus/es/components/table-v2/index")['ElAutoResizer']
export const ElAvatar: typeof import("element-plus/es/components/avatar/index")['ElAvatar']
export const ElBacktop: typeof import("element-plus/es/components/backtop/index")['ElBacktop']
export const ElBadge: typeof import("element-plus/es/components/badge/index")['ElBadge']
export const ElBreadcrumb: typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumb']
export const ElBreadcrumbItem: typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumbItem']
export const ElButton: typeof import("element-plus/es/components/button/index")['ElButton']
export const ElButtonGroup: typeof import("element-plus/es/components/button/index")['ElButtonGroup']
export const ElCalendar: typeof import("element-plus/es/components/calendar/index")['ElCalendar']
export const ElCard: typeof import("element-plus/es/components/card/index")['ElCard']
export const ElCarousel: typeof import("element-plus/es/components/carousel/index")['ElCarousel']
export const ElCarouselItem: typeof import("element-plus/es/components/carousel/index")['ElCarouselItem']
export const ElCascader: typeof import("element-plus/es/components/cascader/index")['ElCascader']
export const ElCascaderPanel: typeof import("element-plus/es/components/cascader-panel/index")['ElCascaderPanel']
export const ElCheckTag: typeof import("element-plus/es/components/check-tag/index")['ElCheckTag']
export const ElCheckbox: typeof import("element-plus/es/components/checkbox/index")['ElCheckbox']
export const ElCheckboxButton: typeof import("element-plus/es/components/checkbox/index")['ElCheckboxButton']
export const ElCheckboxGroup: typeof import("element-plus/es/components/checkbox/index")['ElCheckboxGroup']
export const ElCol: typeof import("element-plus/es/components/col/index")['ElCol']
export const ElCollapse: typeof import("element-plus/es/components/collapse/index")['ElCollapse']
export const ElCollapseItem: typeof import("element-plus/es/components/collapse/index")['ElCollapseItem']
export const ElCollapseTransition: typeof import("element-plus/es/components/collapse-transition/index")['ElCollapseTransition']
export const ElColorPicker: typeof import("element-plus/es/components/color-picker/index")['ElColorPicker']
export const ElConfigProvider: typeof import("element-plus/es/components/config-provider/index")['ElConfigProvider']
export const ElContainer: typeof import("element-plus/es/components/container/index")['ElContainer']
export const ElAside: typeof import("element-plus/es/components/container/index")['ElAside']
export const ElFooter: typeof import("element-plus/es/components/container/index")['ElFooter']
export const ElHeader: typeof import("element-plus/es/components/container/index")['ElHeader']
export const ElMain: typeof import("element-plus/es/components/container/index")['ElMain']
export const ElDatePicker: typeof import("element-plus/es/components/date-picker/index")['ElDatePicker']
export const ElDescriptions: typeof import("element-plus/es/components/descriptions/index")['ElDescriptions']
export const ElDescriptionsItem: typeof import("element-plus/es/components/descriptions/index")['ElDescriptionsItem']
export const ElDialog: typeof import("element-plus/es/components/dialog/index")['ElDialog']
export const ElDivider: typeof import("element-plus/es/components/divider/index")['ElDivider']
export const ElDrawer: typeof import("element-plus/es/components/drawer/index")['ElDrawer']
export const ElDropdown: typeof import("element-plus/es/components/dropdown/index")['ElDropdown']
export const ElDropdownItem: typeof import("element-plus/es/components/dropdown/index")['ElDropdownItem']
export const ElDropdownMenu: typeof import("element-plus/es/components/dropdown/index")['ElDropdownMenu']
export const ElEmpty: typeof import("element-plus/es/components/empty/index")['ElEmpty']
export const ElForm: typeof import("element-plus/es/components/form/index")['ElForm']
export const ElFormItem: typeof import("element-plus/es/components/form/index")['ElFormItem']
export const ElIcon: typeof import("element-plus/es/components/icon/index")['ElIcon']
export const ElImage: typeof import("element-plus/es/components/image/index")['ElImage']
export const ElImageViewer: typeof import("element-plus/es/components/image-viewer/index")['ElImageViewer']
export const ElInput: typeof import("element-plus/es/components/input/index")['ElInput']
export const ElInputNumber: typeof import("element-plus/es/components/input-number/index")['ElInputNumber']
export const ElLink: typeof import("element-plus/es/components/link/index")['ElLink']
export const ElMenu: typeof import("element-plus/es/components/menu/index")['ElMenu']
export const ElMenuItem: typeof import("element-plus/es/components/menu/index")['ElMenuItem']
export const ElMenuItemGroup: typeof import("element-plus/es/components/menu/index")['ElMenuItemGroup']
export const ElSubMenu: typeof import("element-plus/es/components/menu/index")['ElSubMenu']
export const ElPageHeader: typeof import("element-plus/es/components/page-header/index")['ElPageHeader']
export const ElPagination: typeof import("element-plus/es/components/pagination/index")['ElPagination']
export const ElPopconfirm: typeof import("element-plus/es/components/popconfirm/index")['ElPopconfirm']
export const ElPopover: typeof import("element-plus/es/components/popover/index")['ElPopover']
export const ElPopper: typeof import("element-plus/es/components/popper/index")['ElPopper']
export const ElProgress: typeof import("element-plus/es/components/progress/index")['ElProgress']
export const ElRadio: typeof import("element-plus/es/components/radio/index")['ElRadio']
export const ElRadioButton: typeof import("element-plus/es/components/radio/index")['ElRadioButton']
export const ElRadioGroup: typeof import("element-plus/es/components/radio/index")['ElRadioGroup']
export const ElRate: typeof import("element-plus/es/components/rate/index")['ElRate']
export const ElResult: typeof import("element-plus/es/components/result/index")['ElResult']
export const ElRow: typeof import("element-plus/es/components/row/index")['ElRow']
export const ElScrollbar: typeof import("element-plus/es/components/scrollbar/index")['ElScrollbar']
export const ElSelect: typeof import("element-plus/es/components/select/index")['ElSelect']
export const ElOption: typeof import("element-plus/es/components/select/index")['ElOption']
export const ElOptionGroup: typeof import("element-plus/es/components/select/index")['ElOptionGroup']
export const ElSelectV2: typeof import("element-plus/es/components/select-v2/index")['ElSelectV2']
export const ElSkeleton: typeof import("element-plus/es/components/skeleton/index")['ElSkeleton']
export const ElSkeletonItem: typeof import("element-plus/es/components/skeleton/index")['ElSkeletonItem']
export const ElSlider: typeof import("element-plus/es/components/slider/index")['ElSlider']
export const ElSpace: typeof import("element-plus/es/components/space/index")['ElSpace']
export const ElStatistic: typeof import("element-plus/es/components/statistic/index")['ElStatistic']
export const ElCountdown: typeof import("element-plus/es/components/countdown/index")['ElCountdown']
export const ElSteps: typeof import("element-plus/es/components/steps/index")['ElSteps']
export const ElStep: typeof import("element-plus/es/components/steps/index")['ElStep']
export const ElSwitch: typeof import("element-plus/es/components/switch/index")['ElSwitch']
export const ElTable: typeof import("element-plus/es/components/table/index")['ElTable']
export const ElTableColumn: typeof import("element-plus/es/components/table/index")['ElTableColumn']
export const ElTableV2: typeof import("element-plus/es/components/table-v2/index")['ElTableV2']
export const ElTabs: typeof import("element-plus/es/components/tabs/index")['ElTabs']
export const ElTabPane: typeof import("element-plus/es/components/tabs/index")['ElTabPane']
export const ElTag: typeof import("element-plus/es/components/tag/index")['ElTag']
export const ElText: typeof import("element-plus/es/components/text/index")['ElText']
export const ElTimePicker: typeof import("element-plus/es/components/time-picker/index")['ElTimePicker']
export const ElTimeSelect: typeof import("element-plus/es/components/time-select/index")['ElTimeSelect']
export const ElTimeline: typeof import("element-plus/es/components/timeline/index")['ElTimeline']
export const ElTimelineItem: typeof import("element-plus/es/components/timeline/index")['ElTimelineItem']
export const ElTooltip: typeof import("element-plus/es/components/tooltip/index")['ElTooltip']
export const ElTooltipV2: typeof import("element-plus/es/components/tooltip-v2/index")['ElTooltipV2']
export const ElTransfer: typeof import("element-plus/es/components/transfer/index")['ElTransfer']
export const ElTree: typeof import("element-plus/es/components/tree/index")['ElTree']
export const ElTreeSelect: typeof import("element-plus/es/components/tree-select/index")['ElTreeSelect']
export const ElTreeV2: typeof import("element-plus/es/components/tree-v2/index")['ElTreeV2']
export const ElUpload: typeof import("element-plus/es/components/upload/index")['ElUpload']
export const ElIconAddLocation: typeof import("@element-plus/icons-vue")['AddLocation']
export const ElIconAim: typeof import("@element-plus/icons-vue")['Aim']
export const ElIconAlarmClock: typeof import("@element-plus/icons-vue")['AlarmClock']
export const ElIconApple: typeof import("@element-plus/icons-vue")['Apple']
export const ElIconArrowDown: typeof import("@element-plus/icons-vue")['ArrowDown']
export const ElIconArrowDownBold: typeof import("@element-plus/icons-vue")['ArrowDownBold']
export const ElIconArrowLeft: typeof import("@element-plus/icons-vue")['ArrowLeft']
export const ElIconArrowLeftBold: typeof import("@element-plus/icons-vue")['ArrowLeftBold']
export const ElIconArrowRight: typeof import("@element-plus/icons-vue")['ArrowRight']
export const ElIconArrowRightBold: typeof import("@element-plus/icons-vue")['ArrowRightBold']
export const ElIconArrowUp: typeof import("@element-plus/icons-vue")['ArrowUp']
export const ElIconArrowUpBold: typeof import("@element-plus/icons-vue")['ArrowUpBold']
export const ElIconAvatar: typeof import("@element-plus/icons-vue")['Avatar']
export const ElIconBack: typeof import("@element-plus/icons-vue")['Back']
export const ElIconBaseball: typeof import("@element-plus/icons-vue")['Baseball']
export const ElIconBasketball: typeof import("@element-plus/icons-vue")['Basketball']
export const ElIconBell: typeof import("@element-plus/icons-vue")['Bell']
export const ElIconBellFilled: typeof import("@element-plus/icons-vue")['BellFilled']
export const ElIconBicycle: typeof import("@element-plus/icons-vue")['Bicycle']
export const ElIconBottom: typeof import("@element-plus/icons-vue")['Bottom']
export const ElIconBottomLeft: typeof import("@element-plus/icons-vue")['BottomLeft']
export const ElIconBottomRight: typeof import("@element-plus/icons-vue")['BottomRight']
export const ElIconBowl: typeof import("@element-plus/icons-vue")['Bowl']
export const ElIconBox: typeof import("@element-plus/icons-vue")['Box']
export const ElIconBriefcase: typeof import("@element-plus/icons-vue")['Briefcase']
export const ElIconBrush: typeof import("@element-plus/icons-vue")['Brush']
export const ElIconBrushFilled: typeof import("@element-plus/icons-vue")['BrushFilled']
export const ElIconBurger: typeof import("@element-plus/icons-vue")['Burger']
export const ElIconCalendar: typeof import("@element-plus/icons-vue")['Calendar']
export const ElIconCamera: typeof import("@element-plus/icons-vue")['Camera']
export const ElIconCameraFilled: typeof import("@element-plus/icons-vue")['CameraFilled']
export const ElIconCaretBottom: typeof import("@element-plus/icons-vue")['CaretBottom']
export const ElIconCaretLeft: typeof import("@element-plus/icons-vue")['CaretLeft']
export const ElIconCaretRight: typeof import("@element-plus/icons-vue")['CaretRight']
export const ElIconCaretTop: typeof import("@element-plus/icons-vue")['CaretTop']
export const ElIconCellphone: typeof import("@element-plus/icons-vue")['Cellphone']
export const ElIconChatDotRound: typeof import("@element-plus/icons-vue")['ChatDotRound']
export const ElIconChatDotSquare: typeof import("@element-plus/icons-vue")['ChatDotSquare']
export const ElIconChatLineRound: typeof import("@element-plus/icons-vue")['ChatLineRound']
export const ElIconChatLineSquare: typeof import("@element-plus/icons-vue")['ChatLineSquare']
export const ElIconChatRound: typeof import("@element-plus/icons-vue")['ChatRound']
export const ElIconChatSquare: typeof import("@element-plus/icons-vue")['ChatSquare']
export const ElIconCheck: typeof import("@element-plus/icons-vue")['Check']
export const ElIconChecked: typeof import("@element-plus/icons-vue")['Checked']
export const ElIconCherry: typeof import("@element-plus/icons-vue")['Cherry']
export const ElIconChicken: typeof import("@element-plus/icons-vue")['Chicken']
export const ElIconChromeFilled: typeof import("@element-plus/icons-vue")['ChromeFilled']
export const ElIconCircleCheck: typeof import("@element-plus/icons-vue")['CircleCheck']
export const ElIconCircleCheckFilled: typeof import("@element-plus/icons-vue")['CircleCheckFilled']
export const ElIconCircleClose: typeof import("@element-plus/icons-vue")['CircleClose']
export const ElIconCircleCloseFilled: typeof import("@element-plus/icons-vue")['CircleCloseFilled']
export const ElIconCirclePlus: typeof import("@element-plus/icons-vue")['CirclePlus']
export const ElIconCirclePlusFilled: typeof import("@element-plus/icons-vue")['CirclePlusFilled']
export const ElIconClock: typeof import("@element-plus/icons-vue")['Clock']
export const ElIconClose: typeof import("@element-plus/icons-vue")['Close']
export const ElIconCloseBold: typeof import("@element-plus/icons-vue")['CloseBold']
export const ElIconCloudy: typeof import("@element-plus/icons-vue")['Cloudy']
export const ElIconCoffee: typeof import("@element-plus/icons-vue")['Coffee']
export const ElIconCoffeeCup: typeof import("@element-plus/icons-vue")['CoffeeCup']
export const ElIconCoin: typeof import("@element-plus/icons-vue")['Coin']
export const ElIconColdDrink: typeof import("@element-plus/icons-vue")['ColdDrink']
export const ElIconCollection: typeof import("@element-plus/icons-vue")['Collection']
export const ElIconCollectionTag: typeof import("@element-plus/icons-vue")['CollectionTag']
export const ElIconComment: typeof import("@element-plus/icons-vue")['Comment']
export const ElIconCompass: typeof import("@element-plus/icons-vue")['Compass']
export const ElIconConnection: typeof import("@element-plus/icons-vue")['Connection']
export const ElIconCoordinate: typeof import("@element-plus/icons-vue")['Coordinate']
export const ElIconCopyDocument: typeof import("@element-plus/icons-vue")['CopyDocument']
export const ElIconCpu: typeof import("@element-plus/icons-vue")['Cpu']
export const ElIconCreditCard: typeof import("@element-plus/icons-vue")['CreditCard']
export const ElIconCrop: typeof import("@element-plus/icons-vue")['Crop']
export const ElIconDArrowLeft: typeof import("@element-plus/icons-vue")['DArrowLeft']
export const ElIconDArrowRight: typeof import("@element-plus/icons-vue")['DArrowRight']
export const ElIconDCaret: typeof import("@element-plus/icons-vue")['DCaret']
export const ElIconDataAnalysis: typeof import("@element-plus/icons-vue")['DataAnalysis']
export const ElIconDataBoard: typeof import("@element-plus/icons-vue")['DataBoard']
export const ElIconDataLine: typeof import("@element-plus/icons-vue")['DataLine']
export const ElIconDelete: typeof import("@element-plus/icons-vue")['Delete']
export const ElIconDeleteFilled: typeof import("@element-plus/icons-vue")['DeleteFilled']
export const ElIconDeleteLocation: typeof import("@element-plus/icons-vue")['DeleteLocation']
export const ElIconDessert: typeof import("@element-plus/icons-vue")['Dessert']
export const ElIconDiscount: typeof import("@element-plus/icons-vue")['Discount']
export const ElIconDish: typeof import("@element-plus/icons-vue")['Dish']
export const ElIconDishDot: typeof import("@element-plus/icons-vue")['DishDot']
export const ElIconDocument: typeof import("@element-plus/icons-vue")['Document']
export const ElIconDocumentAdd: typeof import("@element-plus/icons-vue")['DocumentAdd']
export const ElIconDocumentChecked: typeof import("@element-plus/icons-vue")['DocumentChecked']
export const ElIconDocumentCopy: typeof import("@element-plus/icons-vue")['DocumentCopy']
export const ElIconDocumentDelete: typeof import("@element-plus/icons-vue")['DocumentDelete']
export const ElIconDocumentRemove: typeof import("@element-plus/icons-vue")['DocumentRemove']
export const ElIconDownload: typeof import("@element-plus/icons-vue")['Download']
export const ElIconDrizzling: typeof import("@element-plus/icons-vue")['Drizzling']
export const ElIconEdit: typeof import("@element-plus/icons-vue")['Edit']
export const ElIconEditPen: typeof import("@element-plus/icons-vue")['EditPen']
export const ElIconEleme: typeof import("@element-plus/icons-vue")['Eleme']
export const ElIconElemeFilled: typeof import("@element-plus/icons-vue")['ElemeFilled']
export const ElIconElementPlus: typeof import("@element-plus/icons-vue")['ElementPlus']
export const ElIconExpand: typeof import("@element-plus/icons-vue")['Expand']
export const ElIconFailed: typeof import("@element-plus/icons-vue")['Failed']
export const ElIconFemale: typeof import("@element-plus/icons-vue")['Female']
export const ElIconFiles: typeof import("@element-plus/icons-vue")['Files']
export const ElIconFilm: typeof import("@element-plus/icons-vue")['Film']
export const ElIconFilter: typeof import("@element-plus/icons-vue")['Filter']
export const ElIconFinished: typeof import("@element-plus/icons-vue")['Finished']
export const ElIconFirstAidKit: typeof import("@element-plus/icons-vue")['FirstAidKit']
export const ElIconFlag: typeof import("@element-plus/icons-vue")['Flag']
export const ElIconFold: typeof import("@element-plus/icons-vue")['Fold']
export const ElIconFolder: typeof import("@element-plus/icons-vue")['Folder']
export const ElIconFolderAdd: typeof import("@element-plus/icons-vue")['FolderAdd']
export const ElIconFolderChecked: typeof import("@element-plus/icons-vue")['FolderChecked']
export const ElIconFolderDelete: typeof import("@element-plus/icons-vue")['FolderDelete']
export const ElIconFolderOpened: typeof import("@element-plus/icons-vue")['FolderOpened']
export const ElIconFolderRemove: typeof import("@element-plus/icons-vue")['FolderRemove']
export const ElIconFood: typeof import("@element-plus/icons-vue")['Food']
export const ElIconFootball: typeof import("@element-plus/icons-vue")['Football']
export const ElIconForkSpoon: typeof import("@element-plus/icons-vue")['ForkSpoon']
export const ElIconFries: typeof import("@element-plus/icons-vue")['Fries']
export const ElIconFullScreen: typeof import("@element-plus/icons-vue")['FullScreen']
export const ElIconGoblet: typeof import("@element-plus/icons-vue")['Goblet']
export const ElIconGobletFull: typeof import("@element-plus/icons-vue")['GobletFull']
export const ElIconGobletSquare: typeof import("@element-plus/icons-vue")['GobletSquare']
export const ElIconGobletSquareFull: typeof import("@element-plus/icons-vue")['GobletSquareFull']
export const ElIconGoldMedal: typeof import("@element-plus/icons-vue")['GoldMedal']
export const ElIconGoods: typeof import("@element-plus/icons-vue")['Goods']
export const ElIconGoodsFilled: typeof import("@element-plus/icons-vue")['GoodsFilled']
export const ElIconGrape: typeof import("@element-plus/icons-vue")['Grape']
export const ElIconGrid: typeof import("@element-plus/icons-vue")['Grid']
export const ElIconGuide: typeof import("@element-plus/icons-vue")['Guide']
export const ElIconHandbag: typeof import("@element-plus/icons-vue")['Handbag']
export const ElIconHeadset: typeof import("@element-plus/icons-vue")['Headset']
export const ElIconHelp: typeof import("@element-plus/icons-vue")['Help']
export const ElIconHelpFilled: typeof import("@element-plus/icons-vue")['HelpFilled']
export const ElIconHide: typeof import("@element-plus/icons-vue")['Hide']
export const ElIconHistogram: typeof import("@element-plus/icons-vue")['Histogram']
export const ElIconHomeFilled: typeof import("@element-plus/icons-vue")['HomeFilled']
export const ElIconHotWater: typeof import("@element-plus/icons-vue")['HotWater']
export const ElIconHouse: typeof import("@element-plus/icons-vue")['House']
export const ElIconIceCream: typeof import("@element-plus/icons-vue")['IceCream']
export const ElIconIceCreamRound: typeof import("@element-plus/icons-vue")['IceCreamRound']
export const ElIconIceCreamSquare: typeof import("@element-plus/icons-vue")['IceCreamSquare']
export const ElIconIceDrink: typeof import("@element-plus/icons-vue")['IceDrink']
export const ElIconIceTea: typeof import("@element-plus/icons-vue")['IceTea']
export const ElIconInfoFilled: typeof import("@element-plus/icons-vue")['InfoFilled']
export const ElIconIphone: typeof import("@element-plus/icons-vue")['Iphone']
export const ElIconKey: typeof import("@element-plus/icons-vue")['Key']
export const ElIconKnifeFork: typeof import("@element-plus/icons-vue")['KnifeFork']
export const ElIconLightning: typeof import("@element-plus/icons-vue")['Lightning']
export const ElIconLink: typeof import("@element-plus/icons-vue")['Link']
export const ElIconList: typeof import("@element-plus/icons-vue")['List']
export const ElIconLoading: typeof import("@element-plus/icons-vue")['Loading']
export const ElIconLocation: typeof import("@element-plus/icons-vue")['Location']
export const ElIconLocationFilled: typeof import("@element-plus/icons-vue")['LocationFilled']
export const ElIconLocationInformation: typeof import("@element-plus/icons-vue")['LocationInformation']
export const ElIconLock: typeof import("@element-plus/icons-vue")['Lock']
export const ElIconLollipop: typeof import("@element-plus/icons-vue")['Lollipop']
export const ElIconMagicStick: typeof import("@element-plus/icons-vue")['MagicStick']
export const ElIconMagnet: typeof import("@element-plus/icons-vue")['Magnet']
export const ElIconMale: typeof import("@element-plus/icons-vue")['Male']
export const ElIconManagement: typeof import("@element-plus/icons-vue")['Management']
export const ElIconMapLocation: typeof import("@element-plus/icons-vue")['MapLocation']
export const ElIconMedal: typeof import("@element-plus/icons-vue")['Medal']
export const ElIconMemo: typeof import("@element-plus/icons-vue")['Memo']
export const ElIconMenu: typeof import("@element-plus/icons-vue")['Menu']
export const ElIconMessage: typeof import("@element-plus/icons-vue")['Message']
export const ElIconMessageBox: typeof import("@element-plus/icons-vue")['MessageBox']
export const ElIconMic: typeof import("@element-plus/icons-vue")['Mic']
export const ElIconMicrophone: typeof import("@element-plus/icons-vue")['Microphone']
export const ElIconMilkTea: typeof import("@element-plus/icons-vue")['MilkTea']
export const ElIconMinus: typeof import("@element-plus/icons-vue")['Minus']
export const ElIconMoney: typeof import("@element-plus/icons-vue")['Money']
export const ElIconMonitor: typeof import("@element-plus/icons-vue")['Monitor']
export const ElIconMoon: typeof import("@element-plus/icons-vue")['Moon']
export const ElIconMoonNight: typeof import("@element-plus/icons-vue")['MoonNight']
export const ElIconMore: typeof import("@element-plus/icons-vue")['More']
export const ElIconMoreFilled: typeof import("@element-plus/icons-vue")['MoreFilled']
export const ElIconMostlyCloudy: typeof import("@element-plus/icons-vue")['MostlyCloudy']
export const ElIconMouse: typeof import("@element-plus/icons-vue")['Mouse']
export const ElIconMug: typeof import("@element-plus/icons-vue")['Mug']
export const ElIconMute: typeof import("@element-plus/icons-vue")['Mute']
export const ElIconMuteNotification: typeof import("@element-plus/icons-vue")['MuteNotification']
export const ElIconNoSmoking: typeof import("@element-plus/icons-vue")['NoSmoking']
export const ElIconNotebook: typeof import("@element-plus/icons-vue")['Notebook']
export const ElIconNotification: typeof import("@element-plus/icons-vue")['Notification']
export const ElIconOdometer: typeof import("@element-plus/icons-vue")['Odometer']
export const ElIconOfficeBuilding: typeof import("@element-plus/icons-vue")['OfficeBuilding']
export const ElIconOpen: typeof import("@element-plus/icons-vue")['Open']
export const ElIconOperation: typeof import("@element-plus/icons-vue")['Operation']
export const ElIconOpportunity: typeof import("@element-plus/icons-vue")['Opportunity']
export const ElIconOrange: typeof import("@element-plus/icons-vue")['Orange']
export const ElIconPaperclip: typeof import("@element-plus/icons-vue")['Paperclip']
export const ElIconPartlyCloudy: typeof import("@element-plus/icons-vue")['PartlyCloudy']
export const ElIconPear: typeof import("@element-plus/icons-vue")['Pear']
export const ElIconPhone: typeof import("@element-plus/icons-vue")['Phone']
export const ElIconPhoneFilled: typeof import("@element-plus/icons-vue")['PhoneFilled']
export const ElIconPicture: typeof import("@element-plus/icons-vue")['Picture']
export const ElIconPictureFilled: typeof import("@element-plus/icons-vue")['PictureFilled']
export const ElIconPictureRounded: typeof import("@element-plus/icons-vue")['PictureRounded']
export const ElIconPieChart: typeof import("@element-plus/icons-vue")['PieChart']
export const ElIconPlace: typeof import("@element-plus/icons-vue")['Place']
export const ElIconPlatform: typeof import("@element-plus/icons-vue")['Platform']
export const ElIconPlus: typeof import("@element-plus/icons-vue")['Plus']
export const ElIconPointer: typeof import("@element-plus/icons-vue")['Pointer']
export const ElIconPosition: typeof import("@element-plus/icons-vue")['Position']
export const ElIconPostcard: typeof import("@element-plus/icons-vue")['Postcard']
export const ElIconPouring: typeof import("@element-plus/icons-vue")['Pouring']
export const ElIconPresent: typeof import("@element-plus/icons-vue")['Present']
export const ElIconPriceTag: typeof import("@element-plus/icons-vue")['PriceTag']
export const ElIconPrinter: typeof import("@element-plus/icons-vue")['Printer']
export const ElIconPromotion: typeof import("@element-plus/icons-vue")['Promotion']
export const ElIconQuartzWatch: typeof import("@element-plus/icons-vue")['QuartzWatch']
export const ElIconQuestionFilled: typeof import("@element-plus/icons-vue")['QuestionFilled']
export const ElIconRank: typeof import("@element-plus/icons-vue")['Rank']
export const ElIconReading: typeof import("@element-plus/icons-vue")['Reading']
export const ElIconReadingLamp: typeof import("@element-plus/icons-vue")['ReadingLamp']
export const ElIconRefresh: typeof import("@element-plus/icons-vue")['Refresh']
export const ElIconRefreshLeft: typeof import("@element-plus/icons-vue")['RefreshLeft']
export const ElIconRefreshRight: typeof import("@element-plus/icons-vue")['RefreshRight']
export const ElIconRefrigerator: typeof import("@element-plus/icons-vue")['Refrigerator']
export const ElIconRemove: typeof import("@element-plus/icons-vue")['Remove']
export const ElIconRemoveFilled: typeof import("@element-plus/icons-vue")['RemoveFilled']
export const ElIconRight: typeof import("@element-plus/icons-vue")['Right']
export const ElIconScaleToOriginal: typeof import("@element-plus/icons-vue")['ScaleToOriginal']
export const ElIconSchool: typeof import("@element-plus/icons-vue")['School']
export const ElIconScissor: typeof import("@element-plus/icons-vue")['Scissor']
export const ElIconSearch: typeof import("@element-plus/icons-vue")['Search']
export const ElIconSelect: typeof import("@element-plus/icons-vue")['Select']
export const ElIconSell: typeof import("@element-plus/icons-vue")['Sell']
export const ElIconSemiSelect: typeof import("@element-plus/icons-vue")['SemiSelect']
export const ElIconService: typeof import("@element-plus/icons-vue")['Service']
export const ElIconSetUp: typeof import("@element-plus/icons-vue")['SetUp']
export const ElIconSetting: typeof import("@element-plus/icons-vue")['Setting']
export const ElIconShare: typeof import("@element-plus/icons-vue")['Share']
export const ElIconShip: typeof import("@element-plus/icons-vue")['Ship']
export const ElIconShop: typeof import("@element-plus/icons-vue")['Shop']
export const ElIconShoppingBag: typeof import("@element-plus/icons-vue")['ShoppingBag']
export const ElIconShoppingCart: typeof import("@element-plus/icons-vue")['ShoppingCart']
export const ElIconShoppingCartFull: typeof import("@element-plus/icons-vue")['ShoppingCartFull']
export const ElIconShoppingTrolley: typeof import("@element-plus/icons-vue")['ShoppingTrolley']
export const ElIconSmoking: typeof import("@element-plus/icons-vue")['Smoking']
export const ElIconSoccer: typeof import("@element-plus/icons-vue")['Soccer']
export const ElIconSoldOut: typeof import("@element-plus/icons-vue")['SoldOut']
export const ElIconSort: typeof import("@element-plus/icons-vue")['Sort']
export const ElIconSortDown: typeof import("@element-plus/icons-vue")['SortDown']
export const ElIconSortUp: typeof import("@element-plus/icons-vue")['SortUp']
export const ElIconStamp: typeof import("@element-plus/icons-vue")['Stamp']
export const ElIconStar: typeof import("@element-plus/icons-vue")['Star']
export const ElIconStarFilled: typeof import("@element-plus/icons-vue")['StarFilled']
export const ElIconStopwatch: typeof import("@element-plus/icons-vue")['Stopwatch']
export const ElIconSuccessFilled: typeof import("@element-plus/icons-vue")['SuccessFilled']
export const ElIconSugar: typeof import("@element-plus/icons-vue")['Sugar']
export const ElIconSuitcase: typeof import("@element-plus/icons-vue")['Suitcase']
export const ElIconSuitcaseLine: typeof import("@element-plus/icons-vue")['SuitcaseLine']
export const ElIconSunny: typeof import("@element-plus/icons-vue")['Sunny']
export const ElIconSunrise: typeof import("@element-plus/icons-vue")['Sunrise']
export const ElIconSunset: typeof import("@element-plus/icons-vue")['Sunset']
export const ElIconSwitch: typeof import("@element-plus/icons-vue")['Switch']
export const ElIconSwitchButton: typeof import("@element-plus/icons-vue")['SwitchButton']
export const ElIconSwitchFilled: typeof import("@element-plus/icons-vue")['SwitchFilled']
export const ElIconTakeawayBox: typeof import("@element-plus/icons-vue")['TakeawayBox']
export const ElIconTicket: typeof import("@element-plus/icons-vue")['Ticket']
export const ElIconTickets: typeof import("@element-plus/icons-vue")['Tickets']
export const ElIconTimer: typeof import("@element-plus/icons-vue")['Timer']
export const ElIconToiletPaper: typeof import("@element-plus/icons-vue")['ToiletPaper']
export const ElIconTools: typeof import("@element-plus/icons-vue")['Tools']
export const ElIconTop: typeof import("@element-plus/icons-vue")['Top']
export const ElIconTopLeft: typeof import("@element-plus/icons-vue")['TopLeft']
export const ElIconTopRight: typeof import("@element-plus/icons-vue")['TopRight']
export const ElIconTrendCharts: typeof import("@element-plus/icons-vue")['TrendCharts']
export const ElIconTrophy: typeof import("@element-plus/icons-vue")['Trophy']
export const ElIconTrophyBase: typeof import("@element-plus/icons-vue")['TrophyBase']
export const ElIconTurnOff: typeof import("@element-plus/icons-vue")['TurnOff']
export const ElIconUmbrella: typeof import("@element-plus/icons-vue")['Umbrella']
export const ElIconUnlock: typeof import("@element-plus/icons-vue")['Unlock']
export const ElIconUpload: typeof import("@element-plus/icons-vue")['Upload']
export const ElIconUploadFilled: typeof import("@element-plus/icons-vue")['UploadFilled']
export const ElIconUser: typeof import("@element-plus/icons-vue")['User']
export const ElIconUserFilled: typeof import("@element-plus/icons-vue")['UserFilled']
export const ElIconVan: typeof import("@element-plus/icons-vue")['Van']
export const ElIconVideoCamera: typeof import("@element-plus/icons-vue")['VideoCamera']
export const ElIconVideoCameraFilled: typeof import("@element-plus/icons-vue")['VideoCameraFilled']
export const ElIconVideoPause: typeof import("@element-plus/icons-vue")['VideoPause']
export const ElIconVideoPlay: typeof import("@element-plus/icons-vue")['VideoPlay']
export const ElIconView: typeof import("@element-plus/icons-vue")['View']
export const ElIconWallet: typeof import("@element-plus/icons-vue")['Wallet']
export const ElIconWalletFilled: typeof import("@element-plus/icons-vue")['WalletFilled']
export const ElIconWarnTriangleFilled: typeof import("@element-plus/icons-vue")['WarnTriangleFilled']
export const ElIconWarning: typeof import("@element-plus/icons-vue")['Warning']
export const ElIconWarningFilled: typeof import("@element-plus/icons-vue")['WarningFilled']
export const ElIconWatch: typeof import("@element-plus/icons-vue")['Watch']
export const ElIconWatermelon: typeof import("@element-plus/icons-vue")['Watermelon']
export const ElIconWindPower: typeof import("@element-plus/icons-vue")['WindPower']
export const ElIconZoomIn: typeof import("@element-plus/icons-vue")['ZoomIn']
export const ElIconZoomOut: typeof import("@element-plus/icons-vue")['ZoomOut']
export const NuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const NoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const Link: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const Base: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const Title: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const Meta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const Style: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const Head: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const Html: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const Body: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']
export const LazyIcon: typeof import("../components/icon/index.vue")['default']
export const LazyLoginDialog: typeof import("../components/login-dialog/index.vue")['default']
export const LazyLogin: typeof import("../components/login-dialog/login.vue")['default']
export const LazyLoginDialogRegister: typeof import("../components/login-dialog/register.vue")['default']
export const LazySidebar: typeof import("../components/sidebar/index.vue")['default']
export const LazySmsCode: typeof import("../components/sms-code/index.vue")['default']
export const LazyUploadFile: typeof import("../components/upload-file/index.vue")['default']
export const LazyNuxtWelcome: typeof import("../node_modules/@nuxt/ui-templates/dist/templates/welcome.vue")['default']
export const LazyNuxtLayout: typeof import("../node_modules/nuxt/dist/app/components/layout")['default']
export const LazyNuxtErrorBoundary: typeof import("../node_modules/nuxt/dist/app/components/nuxt-error-boundary")['default']
export const LazyClientOnly: typeof import("../node_modules/nuxt/dist/app/components/client-only")['default']
export const LazyDevOnly: typeof import("../node_modules/nuxt/dist/app/components/dev-only")['default']
export const LazyServerPlaceholder: typeof import("../node_modules/nuxt/dist/app/components/server-placeholder")['default']
export const LazyNuxtLink: typeof import("../node_modules/nuxt/dist/app/components/nuxt-link")['default']
export const LazyNuxtLoadingIndicator: typeof import("../node_modules/nuxt/dist/app/components/nuxt-loading-indicator")['default']
export const LazyElAffix: typeof import("element-plus/es/components/affix/index")['ElAffix']
export const LazyElAlert: typeof import("element-plus/es/components/alert/index")['ElAlert']
export const LazyElAutocomplete: typeof import("element-plus/es/components/autocomplete/index")['ElAutocomplete']
export const LazyElAutoResizer: typeof import("element-plus/es/components/table-v2/index")['ElAutoResizer']
export const LazyElAvatar: typeof import("element-plus/es/components/avatar/index")['ElAvatar']
export const LazyElBacktop: typeof import("element-plus/es/components/backtop/index")['ElBacktop']
export const LazyElBadge: typeof import("element-plus/es/components/badge/index")['ElBadge']
export const LazyElBreadcrumb: typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumb']
export const LazyElBreadcrumbItem: typeof import("element-plus/es/components/breadcrumb/index")['ElBreadcrumbItem']
export const LazyElButton: typeof import("element-plus/es/components/button/index")['ElButton']
export const LazyElButtonGroup: typeof import("element-plus/es/components/button/index")['ElButtonGroup']
export const LazyElCalendar: typeof import("element-plus/es/components/calendar/index")['ElCalendar']
export const LazyElCard: typeof import("element-plus/es/components/card/index")['ElCard']
export const LazyElCarousel: typeof import("element-plus/es/components/carousel/index")['ElCarousel']
export const LazyElCarouselItem: typeof import("element-plus/es/components/carousel/index")['ElCarouselItem']
export const LazyElCascader: typeof import("element-plus/es/components/cascader/index")['ElCascader']
export const LazyElCascaderPanel: typeof import("element-plus/es/components/cascader-panel/index")['ElCascaderPanel']
export const LazyElCheckTag: typeof import("element-plus/es/components/check-tag/index")['ElCheckTag']
export const LazyElCheckbox: typeof import("element-plus/es/components/checkbox/index")['ElCheckbox']
export const LazyElCheckboxButton: typeof import("element-plus/es/components/checkbox/index")['ElCheckboxButton']
export const LazyElCheckboxGroup: typeof import("element-plus/es/components/checkbox/index")['ElCheckboxGroup']
export const LazyElCol: typeof import("element-plus/es/components/col/index")['ElCol']
export const LazyElCollapse: typeof import("element-plus/es/components/collapse/index")['ElCollapse']
export const LazyElCollapseItem: typeof import("element-plus/es/components/collapse/index")['ElCollapseItem']
export const LazyElCollapseTransition: typeof import("element-plus/es/components/collapse-transition/index")['ElCollapseTransition']
export const LazyElColorPicker: typeof import("element-plus/es/components/color-picker/index")['ElColorPicker']
export const LazyElConfigProvider: typeof import("element-plus/es/components/config-provider/index")['ElConfigProvider']
export const LazyElContainer: typeof import("element-plus/es/components/container/index")['ElContainer']
export const LazyElAside: typeof import("element-plus/es/components/container/index")['ElAside']
export const LazyElFooter: typeof import("element-plus/es/components/container/index")['ElFooter']
export const LazyElHeader: typeof import("element-plus/es/components/container/index")['ElHeader']
export const LazyElMain: typeof import("element-plus/es/components/container/index")['ElMain']
export const LazyElDatePicker: typeof import("element-plus/es/components/date-picker/index")['ElDatePicker']
export const LazyElDescriptions: typeof import("element-plus/es/components/descriptions/index")['ElDescriptions']
export const LazyElDescriptionsItem: typeof import("element-plus/es/components/descriptions/index")['ElDescriptionsItem']
export const LazyElDialog: typeof import("element-plus/es/components/dialog/index")['ElDialog']
export const LazyElDivider: typeof import("element-plus/es/components/divider/index")['ElDivider']
export const LazyElDrawer: typeof import("element-plus/es/components/drawer/index")['ElDrawer']
export const LazyElDropdown: typeof import("element-plus/es/components/dropdown/index")['ElDropdown']
export const LazyElDropdownItem: typeof import("element-plus/es/components/dropdown/index")['ElDropdownItem']
export const LazyElDropdownMenu: typeof import("element-plus/es/components/dropdown/index")['ElDropdownMenu']
export const LazyElEmpty: typeof import("element-plus/es/components/empty/index")['ElEmpty']
export const LazyElForm: typeof import("element-plus/es/components/form/index")['ElForm']
export const LazyElFormItem: typeof import("element-plus/es/components/form/index")['ElFormItem']
export const LazyElIcon: typeof import("element-plus/es/components/icon/index")['ElIcon']
export const LazyElImage: typeof import("element-plus/es/components/image/index")['ElImage']
export const LazyElImageViewer: typeof import("element-plus/es/components/image-viewer/index")['ElImageViewer']
export const LazyElInput: typeof import("element-plus/es/components/input/index")['ElInput']
export const LazyElInputNumber: typeof import("element-plus/es/components/input-number/index")['ElInputNumber']
export const LazyElLink: typeof import("element-plus/es/components/link/index")['ElLink']
export const LazyElMenu: typeof import("element-plus/es/components/menu/index")['ElMenu']
export const LazyElMenuItem: typeof import("element-plus/es/components/menu/index")['ElMenuItem']
export const LazyElMenuItemGroup: typeof import("element-plus/es/components/menu/index")['ElMenuItemGroup']
export const LazyElSubMenu: typeof import("element-plus/es/components/menu/index")['ElSubMenu']
export const LazyElPageHeader: typeof import("element-plus/es/components/page-header/index")['ElPageHeader']
export const LazyElPagination: typeof import("element-plus/es/components/pagination/index")['ElPagination']
export const LazyElPopconfirm: typeof import("element-plus/es/components/popconfirm/index")['ElPopconfirm']
export const LazyElPopover: typeof import("element-plus/es/components/popover/index")['ElPopover']
export const LazyElPopper: typeof import("element-plus/es/components/popper/index")['ElPopper']
export const LazyElProgress: typeof import("element-plus/es/components/progress/index")['ElProgress']
export const LazyElRadio: typeof import("element-plus/es/components/radio/index")['ElRadio']
export const LazyElRadioButton: typeof import("element-plus/es/components/radio/index")['ElRadioButton']
export const LazyElRadioGroup: typeof import("element-plus/es/components/radio/index")['ElRadioGroup']
export const LazyElRate: typeof import("element-plus/es/components/rate/index")['ElRate']
export const LazyElResult: typeof import("element-plus/es/components/result/index")['ElResult']
export const LazyElRow: typeof import("element-plus/es/components/row/index")['ElRow']
export const LazyElScrollbar: typeof import("element-plus/es/components/scrollbar/index")['ElScrollbar']
export const LazyElSelect: typeof import("element-plus/es/components/select/index")['ElSelect']
export const LazyElOption: typeof import("element-plus/es/components/select/index")['ElOption']
export const LazyElOptionGroup: typeof import("element-plus/es/components/select/index")['ElOptionGroup']
export const LazyElSelectV2: typeof import("element-plus/es/components/select-v2/index")['ElSelectV2']
export const LazyElSkeleton: typeof import("element-plus/es/components/skeleton/index")['ElSkeleton']
export const LazyElSkeletonItem: typeof import("element-plus/es/components/skeleton/index")['ElSkeletonItem']
export const LazyElSlider: typeof import("element-plus/es/components/slider/index")['ElSlider']
export const LazyElSpace: typeof import("element-plus/es/components/space/index")['ElSpace']
export const LazyElStatistic: typeof import("element-plus/es/components/statistic/index")['ElStatistic']
export const LazyElCountdown: typeof import("element-plus/es/components/countdown/index")['ElCountdown']
export const LazyElSteps: typeof import("element-plus/es/components/steps/index")['ElSteps']
export const LazyElStep: typeof import("element-plus/es/components/steps/index")['ElStep']
export const LazyElSwitch: typeof import("element-plus/es/components/switch/index")['ElSwitch']
export const LazyElTable: typeof import("element-plus/es/components/table/index")['ElTable']
export const LazyElTableColumn: typeof import("element-plus/es/components/table/index")['ElTableColumn']
export const LazyElTableV2: typeof import("element-plus/es/components/table-v2/index")['ElTableV2']
export const LazyElTabs: typeof import("element-plus/es/components/tabs/index")['ElTabs']
export const LazyElTabPane: typeof import("element-plus/es/components/tabs/index")['ElTabPane']
export const LazyElTag: typeof import("element-plus/es/components/tag/index")['ElTag']
export const LazyElText: typeof import("element-plus/es/components/text/index")['ElText']
export const LazyElTimePicker: typeof import("element-plus/es/components/time-picker/index")['ElTimePicker']
export const LazyElTimeSelect: typeof import("element-plus/es/components/time-select/index")['ElTimeSelect']
export const LazyElTimeline: typeof import("element-plus/es/components/timeline/index")['ElTimeline']
export const LazyElTimelineItem: typeof import("element-plus/es/components/timeline/index")['ElTimelineItem']
export const LazyElTooltip: typeof import("element-plus/es/components/tooltip/index")['ElTooltip']
export const LazyElTooltipV2: typeof import("element-plus/es/components/tooltip-v2/index")['ElTooltipV2']
export const LazyElTransfer: typeof import("element-plus/es/components/transfer/index")['ElTransfer']
export const LazyElTree: typeof import("element-plus/es/components/tree/index")['ElTree']
export const LazyElTreeSelect: typeof import("element-plus/es/components/tree-select/index")['ElTreeSelect']
export const LazyElTreeV2: typeof import("element-plus/es/components/tree-v2/index")['ElTreeV2']
export const LazyElUpload: typeof import("element-plus/es/components/upload/index")['ElUpload']
export const LazyElIconAddLocation: typeof import("@element-plus/icons-vue")['AddLocation']
export const LazyElIconAim: typeof import("@element-plus/icons-vue")['Aim']
export const LazyElIconAlarmClock: typeof import("@element-plus/icons-vue")['AlarmClock']
export const LazyElIconApple: typeof import("@element-plus/icons-vue")['Apple']
export const LazyElIconArrowDown: typeof import("@element-plus/icons-vue")['ArrowDown']
export const LazyElIconArrowDownBold: typeof import("@element-plus/icons-vue")['ArrowDownBold']
export const LazyElIconArrowLeft: typeof import("@element-plus/icons-vue")['ArrowLeft']
export const LazyElIconArrowLeftBold: typeof import("@element-plus/icons-vue")['ArrowLeftBold']
export const LazyElIconArrowRight: typeof import("@element-plus/icons-vue")['ArrowRight']
export const LazyElIconArrowRightBold: typeof import("@element-plus/icons-vue")['ArrowRightBold']
export const LazyElIconArrowUp: typeof import("@element-plus/icons-vue")['ArrowUp']
export const LazyElIconArrowUpBold: typeof import("@element-plus/icons-vue")['ArrowUpBold']
export const LazyElIconAvatar: typeof import("@element-plus/icons-vue")['Avatar']
export const LazyElIconBack: typeof import("@element-plus/icons-vue")['Back']
export const LazyElIconBaseball: typeof import("@element-plus/icons-vue")['Baseball']
export const LazyElIconBasketball: typeof import("@element-plus/icons-vue")['Basketball']
export const LazyElIconBell: typeof import("@element-plus/icons-vue")['Bell']
export const LazyElIconBellFilled: typeof import("@element-plus/icons-vue")['BellFilled']
export const LazyElIconBicycle: typeof import("@element-plus/icons-vue")['Bicycle']
export const LazyElIconBottom: typeof import("@element-plus/icons-vue")['Bottom']
export const LazyElIconBottomLeft: typeof import("@element-plus/icons-vue")['BottomLeft']
export const LazyElIconBottomRight: typeof import("@element-plus/icons-vue")['BottomRight']
export const LazyElIconBowl: typeof import("@element-plus/icons-vue")['Bowl']
export const LazyElIconBox: typeof import("@element-plus/icons-vue")['Box']
export const LazyElIconBriefcase: typeof import("@element-plus/icons-vue")['Briefcase']
export const LazyElIconBrush: typeof import("@element-plus/icons-vue")['Brush']
export const LazyElIconBrushFilled: typeof import("@element-plus/icons-vue")['BrushFilled']
export const LazyElIconBurger: typeof import("@element-plus/icons-vue")['Burger']
export const LazyElIconCalendar: typeof import("@element-plus/icons-vue")['Calendar']
export const LazyElIconCamera: typeof import("@element-plus/icons-vue")['Camera']
export const LazyElIconCameraFilled: typeof import("@element-plus/icons-vue")['CameraFilled']
export const LazyElIconCaretBottom: typeof import("@element-plus/icons-vue")['CaretBottom']
export const LazyElIconCaretLeft: typeof import("@element-plus/icons-vue")['CaretLeft']
export const LazyElIconCaretRight: typeof import("@element-plus/icons-vue")['CaretRight']
export const LazyElIconCaretTop: typeof import("@element-plus/icons-vue")['CaretTop']
export const LazyElIconCellphone: typeof import("@element-plus/icons-vue")['Cellphone']
export const LazyElIconChatDotRound: typeof import("@element-plus/icons-vue")['ChatDotRound']
export const LazyElIconChatDotSquare: typeof import("@element-plus/icons-vue")['ChatDotSquare']
export const LazyElIconChatLineRound: typeof import("@element-plus/icons-vue")['ChatLineRound']
export const LazyElIconChatLineSquare: typeof import("@element-plus/icons-vue")['ChatLineSquare']
export const LazyElIconChatRound: typeof import("@element-plus/icons-vue")['ChatRound']
export const LazyElIconChatSquare: typeof import("@element-plus/icons-vue")['ChatSquare']
export const LazyElIconCheck: typeof import("@element-plus/icons-vue")['Check']
export const LazyElIconChecked: typeof import("@element-plus/icons-vue")['Checked']
export const LazyElIconCherry: typeof import("@element-plus/icons-vue")['Cherry']
export const LazyElIconChicken: typeof import("@element-plus/icons-vue")['Chicken']
export const LazyElIconChromeFilled: typeof import("@element-plus/icons-vue")['ChromeFilled']
export const LazyElIconCircleCheck: typeof import("@element-plus/icons-vue")['CircleCheck']
export const LazyElIconCircleCheckFilled: typeof import("@element-plus/icons-vue")['CircleCheckFilled']
export const LazyElIconCircleClose: typeof import("@element-plus/icons-vue")['CircleClose']
export const LazyElIconCircleCloseFilled: typeof import("@element-plus/icons-vue")['CircleCloseFilled']
export const LazyElIconCirclePlus: typeof import("@element-plus/icons-vue")['CirclePlus']
export const LazyElIconCirclePlusFilled: typeof import("@element-plus/icons-vue")['CirclePlusFilled']
export const LazyElIconClock: typeof import("@element-plus/icons-vue")['Clock']
export const LazyElIconClose: typeof import("@element-plus/icons-vue")['Close']
export const LazyElIconCloseBold: typeof import("@element-plus/icons-vue")['CloseBold']
export const LazyElIconCloudy: typeof import("@element-plus/icons-vue")['Cloudy']
export const LazyElIconCoffee: typeof import("@element-plus/icons-vue")['Coffee']
export const LazyElIconCoffeeCup: typeof import("@element-plus/icons-vue")['CoffeeCup']
export const LazyElIconCoin: typeof import("@element-plus/icons-vue")['Coin']
export const LazyElIconColdDrink: typeof import("@element-plus/icons-vue")['ColdDrink']
export const LazyElIconCollection: typeof import("@element-plus/icons-vue")['Collection']
export const LazyElIconCollectionTag: typeof import("@element-plus/icons-vue")['CollectionTag']
export const LazyElIconComment: typeof import("@element-plus/icons-vue")['Comment']
export const LazyElIconCompass: typeof import("@element-plus/icons-vue")['Compass']
export const LazyElIconConnection: typeof import("@element-plus/icons-vue")['Connection']
export const LazyElIconCoordinate: typeof import("@element-plus/icons-vue")['Coordinate']
export const LazyElIconCopyDocument: typeof import("@element-plus/icons-vue")['CopyDocument']
export const LazyElIconCpu: typeof import("@element-plus/icons-vue")['Cpu']
export const LazyElIconCreditCard: typeof import("@element-plus/icons-vue")['CreditCard']
export const LazyElIconCrop: typeof import("@element-plus/icons-vue")['Crop']
export const LazyElIconDArrowLeft: typeof import("@element-plus/icons-vue")['DArrowLeft']
export const LazyElIconDArrowRight: typeof import("@element-plus/icons-vue")['DArrowRight']
export const LazyElIconDCaret: typeof import("@element-plus/icons-vue")['DCaret']
export const LazyElIconDataAnalysis: typeof import("@element-plus/icons-vue")['DataAnalysis']
export const LazyElIconDataBoard: typeof import("@element-plus/icons-vue")['DataBoard']
export const LazyElIconDataLine: typeof import("@element-plus/icons-vue")['DataLine']
export const LazyElIconDelete: typeof import("@element-plus/icons-vue")['Delete']
export const LazyElIconDeleteFilled: typeof import("@element-plus/icons-vue")['DeleteFilled']
export const LazyElIconDeleteLocation: typeof import("@element-plus/icons-vue")['DeleteLocation']
export const LazyElIconDessert: typeof import("@element-plus/icons-vue")['Dessert']
export const LazyElIconDiscount: typeof import("@element-plus/icons-vue")['Discount']
export const LazyElIconDish: typeof import("@element-plus/icons-vue")['Dish']
export const LazyElIconDishDot: typeof import("@element-plus/icons-vue")['DishDot']
export const LazyElIconDocument: typeof import("@element-plus/icons-vue")['Document']
export const LazyElIconDocumentAdd: typeof import("@element-plus/icons-vue")['DocumentAdd']
export const LazyElIconDocumentChecked: typeof import("@element-plus/icons-vue")['DocumentChecked']
export const LazyElIconDocumentCopy: typeof import("@element-plus/icons-vue")['DocumentCopy']
export const LazyElIconDocumentDelete: typeof import("@element-plus/icons-vue")['DocumentDelete']
export const LazyElIconDocumentRemove: typeof import("@element-plus/icons-vue")['DocumentRemove']
export const LazyElIconDownload: typeof import("@element-plus/icons-vue")['Download']
export const LazyElIconDrizzling: typeof import("@element-plus/icons-vue")['Drizzling']
export const LazyElIconEdit: typeof import("@element-plus/icons-vue")['Edit']
export const LazyElIconEditPen: typeof import("@element-plus/icons-vue")['EditPen']
export const LazyElIconEleme: typeof import("@element-plus/icons-vue")['Eleme']
export const LazyElIconElemeFilled: typeof import("@element-plus/icons-vue")['ElemeFilled']
export const LazyElIconElementPlus: typeof import("@element-plus/icons-vue")['ElementPlus']
export const LazyElIconExpand: typeof import("@element-plus/icons-vue")['Expand']
export const LazyElIconFailed: typeof import("@element-plus/icons-vue")['Failed']
export const LazyElIconFemale: typeof import("@element-plus/icons-vue")['Female']
export const LazyElIconFiles: typeof import("@element-plus/icons-vue")['Files']
export const LazyElIconFilm: typeof import("@element-plus/icons-vue")['Film']
export const LazyElIconFilter: typeof import("@element-plus/icons-vue")['Filter']
export const LazyElIconFinished: typeof import("@element-plus/icons-vue")['Finished']
export const LazyElIconFirstAidKit: typeof import("@element-plus/icons-vue")['FirstAidKit']
export const LazyElIconFlag: typeof import("@element-plus/icons-vue")['Flag']
export const LazyElIconFold: typeof import("@element-plus/icons-vue")['Fold']
export const LazyElIconFolder: typeof import("@element-plus/icons-vue")['Folder']
export const LazyElIconFolderAdd: typeof import("@element-plus/icons-vue")['FolderAdd']
export const LazyElIconFolderChecked: typeof import("@element-plus/icons-vue")['FolderChecked']
export const LazyElIconFolderDelete: typeof import("@element-plus/icons-vue")['FolderDelete']
export const LazyElIconFolderOpened: typeof import("@element-plus/icons-vue")['FolderOpened']
export const LazyElIconFolderRemove: typeof import("@element-plus/icons-vue")['FolderRemove']
export const LazyElIconFood: typeof import("@element-plus/icons-vue")['Food']
export const LazyElIconFootball: typeof import("@element-plus/icons-vue")['Football']
export const LazyElIconForkSpoon: typeof import("@element-plus/icons-vue")['ForkSpoon']
export const LazyElIconFries: typeof import("@element-plus/icons-vue")['Fries']
export const LazyElIconFullScreen: typeof import("@element-plus/icons-vue")['FullScreen']
export const LazyElIconGoblet: typeof import("@element-plus/icons-vue")['Goblet']
export const LazyElIconGobletFull: typeof import("@element-plus/icons-vue")['GobletFull']
export const LazyElIconGobletSquare: typeof import("@element-plus/icons-vue")['GobletSquare']
export const LazyElIconGobletSquareFull: typeof import("@element-plus/icons-vue")['GobletSquareFull']
export const LazyElIconGoldMedal: typeof import("@element-plus/icons-vue")['GoldMedal']
export const LazyElIconGoods: typeof import("@element-plus/icons-vue")['Goods']
export const LazyElIconGoodsFilled: typeof import("@element-plus/icons-vue")['GoodsFilled']
export const LazyElIconGrape: typeof import("@element-plus/icons-vue")['Grape']
export const LazyElIconGrid: typeof import("@element-plus/icons-vue")['Grid']
export const LazyElIconGuide: typeof import("@element-plus/icons-vue")['Guide']
export const LazyElIconHandbag: typeof import("@element-plus/icons-vue")['Handbag']
export const LazyElIconHeadset: typeof import("@element-plus/icons-vue")['Headset']
export const LazyElIconHelp: typeof import("@element-plus/icons-vue")['Help']
export const LazyElIconHelpFilled: typeof import("@element-plus/icons-vue")['HelpFilled']
export const LazyElIconHide: typeof import("@element-plus/icons-vue")['Hide']
export const LazyElIconHistogram: typeof import("@element-plus/icons-vue")['Histogram']
export const LazyElIconHomeFilled: typeof import("@element-plus/icons-vue")['HomeFilled']
export const LazyElIconHotWater: typeof import("@element-plus/icons-vue")['HotWater']
export const LazyElIconHouse: typeof import("@element-plus/icons-vue")['House']
export const LazyElIconIceCream: typeof import("@element-plus/icons-vue")['IceCream']
export const LazyElIconIceCreamRound: typeof import("@element-plus/icons-vue")['IceCreamRound']
export const LazyElIconIceCreamSquare: typeof import("@element-plus/icons-vue")['IceCreamSquare']
export const LazyElIconIceDrink: typeof import("@element-plus/icons-vue")['IceDrink']
export const LazyElIconIceTea: typeof import("@element-plus/icons-vue")['IceTea']
export const LazyElIconInfoFilled: typeof import("@element-plus/icons-vue")['InfoFilled']
export const LazyElIconIphone: typeof import("@element-plus/icons-vue")['Iphone']
export const LazyElIconKey: typeof import("@element-plus/icons-vue")['Key']
export const LazyElIconKnifeFork: typeof import("@element-plus/icons-vue")['KnifeFork']
export const LazyElIconLightning: typeof import("@element-plus/icons-vue")['Lightning']
export const LazyElIconLink: typeof import("@element-plus/icons-vue")['Link']
export const LazyElIconList: typeof import("@element-plus/icons-vue")['List']
export const LazyElIconLoading: typeof import("@element-plus/icons-vue")['Loading']
export const LazyElIconLocation: typeof import("@element-plus/icons-vue")['Location']
export const LazyElIconLocationFilled: typeof import("@element-plus/icons-vue")['LocationFilled']
export const LazyElIconLocationInformation: typeof import("@element-plus/icons-vue")['LocationInformation']
export const LazyElIconLock: typeof import("@element-plus/icons-vue")['Lock']
export const LazyElIconLollipop: typeof import("@element-plus/icons-vue")['Lollipop']
export const LazyElIconMagicStick: typeof import("@element-plus/icons-vue")['MagicStick']
export const LazyElIconMagnet: typeof import("@element-plus/icons-vue")['Magnet']
export const LazyElIconMale: typeof import("@element-plus/icons-vue")['Male']
export const LazyElIconManagement: typeof import("@element-plus/icons-vue")['Management']
export const LazyElIconMapLocation: typeof import("@element-plus/icons-vue")['MapLocation']
export const LazyElIconMedal: typeof import("@element-plus/icons-vue")['Medal']
export const LazyElIconMemo: typeof import("@element-plus/icons-vue")['Memo']
export const LazyElIconMenu: typeof import("@element-plus/icons-vue")['Menu']
export const LazyElIconMessage: typeof import("@element-plus/icons-vue")['Message']
export const LazyElIconMessageBox: typeof import("@element-plus/icons-vue")['MessageBox']
export const LazyElIconMic: typeof import("@element-plus/icons-vue")['Mic']
export const LazyElIconMicrophone: typeof import("@element-plus/icons-vue")['Microphone']
export const LazyElIconMilkTea: typeof import("@element-plus/icons-vue")['MilkTea']
export const LazyElIconMinus: typeof import("@element-plus/icons-vue")['Minus']
export const LazyElIconMoney: typeof import("@element-plus/icons-vue")['Money']
export const LazyElIconMonitor: typeof import("@element-plus/icons-vue")['Monitor']
export const LazyElIconMoon: typeof import("@element-plus/icons-vue")['Moon']
export const LazyElIconMoonNight: typeof import("@element-plus/icons-vue")['MoonNight']
export const LazyElIconMore: typeof import("@element-plus/icons-vue")['More']
export const LazyElIconMoreFilled: typeof import("@element-plus/icons-vue")['MoreFilled']
export const LazyElIconMostlyCloudy: typeof import("@element-plus/icons-vue")['MostlyCloudy']
export const LazyElIconMouse: typeof import("@element-plus/icons-vue")['Mouse']
export const LazyElIconMug: typeof import("@element-plus/icons-vue")['Mug']
export const LazyElIconMute: typeof import("@element-plus/icons-vue")['Mute']
export const LazyElIconMuteNotification: typeof import("@element-plus/icons-vue")['MuteNotification']
export const LazyElIconNoSmoking: typeof import("@element-plus/icons-vue")['NoSmoking']
export const LazyElIconNotebook: typeof import("@element-plus/icons-vue")['Notebook']
export const LazyElIconNotification: typeof import("@element-plus/icons-vue")['Notification']
export const LazyElIconOdometer: typeof import("@element-plus/icons-vue")['Odometer']
export const LazyElIconOfficeBuilding: typeof import("@element-plus/icons-vue")['OfficeBuilding']
export const LazyElIconOpen: typeof import("@element-plus/icons-vue")['Open']
export const LazyElIconOperation: typeof import("@element-plus/icons-vue")['Operation']
export const LazyElIconOpportunity: typeof import("@element-plus/icons-vue")['Opportunity']
export const LazyElIconOrange: typeof import("@element-plus/icons-vue")['Orange']
export const LazyElIconPaperclip: typeof import("@element-plus/icons-vue")['Paperclip']
export const LazyElIconPartlyCloudy: typeof import("@element-plus/icons-vue")['PartlyCloudy']
export const LazyElIconPear: typeof import("@element-plus/icons-vue")['Pear']
export const LazyElIconPhone: typeof import("@element-plus/icons-vue")['Phone']
export const LazyElIconPhoneFilled: typeof import("@element-plus/icons-vue")['PhoneFilled']
export const LazyElIconPicture: typeof import("@element-plus/icons-vue")['Picture']
export const LazyElIconPictureFilled: typeof import("@element-plus/icons-vue")['PictureFilled']
export const LazyElIconPictureRounded: typeof import("@element-plus/icons-vue")['PictureRounded']
export const LazyElIconPieChart: typeof import("@element-plus/icons-vue")['PieChart']
export const LazyElIconPlace: typeof import("@element-plus/icons-vue")['Place']
export const LazyElIconPlatform: typeof import("@element-plus/icons-vue")['Platform']
export const LazyElIconPlus: typeof import("@element-plus/icons-vue")['Plus']
export const LazyElIconPointer: typeof import("@element-plus/icons-vue")['Pointer']
export const LazyElIconPosition: typeof import("@element-plus/icons-vue")['Position']
export const LazyElIconPostcard: typeof import("@element-plus/icons-vue")['Postcard']
export const LazyElIconPouring: typeof import("@element-plus/icons-vue")['Pouring']
export const LazyElIconPresent: typeof import("@element-plus/icons-vue")['Present']
export const LazyElIconPriceTag: typeof import("@element-plus/icons-vue")['PriceTag']
export const LazyElIconPrinter: typeof import("@element-plus/icons-vue")['Printer']
export const LazyElIconPromotion: typeof import("@element-plus/icons-vue")['Promotion']
export const LazyElIconQuartzWatch: typeof import("@element-plus/icons-vue")['QuartzWatch']
export const LazyElIconQuestionFilled: typeof import("@element-plus/icons-vue")['QuestionFilled']
export const LazyElIconRank: typeof import("@element-plus/icons-vue")['Rank']
export const LazyElIconReading: typeof import("@element-plus/icons-vue")['Reading']
export const LazyElIconReadingLamp: typeof import("@element-plus/icons-vue")['ReadingLamp']
export const LazyElIconRefresh: typeof import("@element-plus/icons-vue")['Refresh']
export const LazyElIconRefreshLeft: typeof import("@element-plus/icons-vue")['RefreshLeft']
export const LazyElIconRefreshRight: typeof import("@element-plus/icons-vue")['RefreshRight']
export const LazyElIconRefrigerator: typeof import("@element-plus/icons-vue")['Refrigerator']
export const LazyElIconRemove: typeof import("@element-plus/icons-vue")['Remove']
export const LazyElIconRemoveFilled: typeof import("@element-plus/icons-vue")['RemoveFilled']
export const LazyElIconRight: typeof import("@element-plus/icons-vue")['Right']
export const LazyElIconScaleToOriginal: typeof import("@element-plus/icons-vue")['ScaleToOriginal']
export const LazyElIconSchool: typeof import("@element-plus/icons-vue")['School']
export const LazyElIconScissor: typeof import("@element-plus/icons-vue")['Scissor']
export const LazyElIconSearch: typeof import("@element-plus/icons-vue")['Search']
export const LazyElIconSelect: typeof import("@element-plus/icons-vue")['Select']
export const LazyElIconSell: typeof import("@element-plus/icons-vue")['Sell']
export const LazyElIconSemiSelect: typeof import("@element-plus/icons-vue")['SemiSelect']
export const LazyElIconService: typeof import("@element-plus/icons-vue")['Service']
export const LazyElIconSetUp: typeof import("@element-plus/icons-vue")['SetUp']
export const LazyElIconSetting: typeof import("@element-plus/icons-vue")['Setting']
export const LazyElIconShare: typeof import("@element-plus/icons-vue")['Share']
export const LazyElIconShip: typeof import("@element-plus/icons-vue")['Ship']
export const LazyElIconShop: typeof import("@element-plus/icons-vue")['Shop']
export const LazyElIconShoppingBag: typeof import("@element-plus/icons-vue")['ShoppingBag']
export const LazyElIconShoppingCart: typeof import("@element-plus/icons-vue")['ShoppingCart']
export const LazyElIconShoppingCartFull: typeof import("@element-plus/icons-vue")['ShoppingCartFull']
export const LazyElIconShoppingTrolley: typeof import("@element-plus/icons-vue")['ShoppingTrolley']
export const LazyElIconSmoking: typeof import("@element-plus/icons-vue")['Smoking']
export const LazyElIconSoccer: typeof import("@element-plus/icons-vue")['Soccer']
export const LazyElIconSoldOut: typeof import("@element-plus/icons-vue")['SoldOut']
export const LazyElIconSort: typeof import("@element-plus/icons-vue")['Sort']
export const LazyElIconSortDown: typeof import("@element-plus/icons-vue")['SortDown']
export const LazyElIconSortUp: typeof import("@element-plus/icons-vue")['SortUp']
export const LazyElIconStamp: typeof import("@element-plus/icons-vue")['Stamp']
export const LazyElIconStar: typeof import("@element-plus/icons-vue")['Star']
export const LazyElIconStarFilled: typeof import("@element-plus/icons-vue")['StarFilled']
export const LazyElIconStopwatch: typeof import("@element-plus/icons-vue")['Stopwatch']
export const LazyElIconSuccessFilled: typeof import("@element-plus/icons-vue")['SuccessFilled']
export const LazyElIconSugar: typeof import("@element-plus/icons-vue")['Sugar']
export const LazyElIconSuitcase: typeof import("@element-plus/icons-vue")['Suitcase']
export const LazyElIconSuitcaseLine: typeof import("@element-plus/icons-vue")['SuitcaseLine']
export const LazyElIconSunny: typeof import("@element-plus/icons-vue")['Sunny']
export const LazyElIconSunrise: typeof import("@element-plus/icons-vue")['Sunrise']
export const LazyElIconSunset: typeof import("@element-plus/icons-vue")['Sunset']
export const LazyElIconSwitch: typeof import("@element-plus/icons-vue")['Switch']
export const LazyElIconSwitchButton: typeof import("@element-plus/icons-vue")['SwitchButton']
export const LazyElIconSwitchFilled: typeof import("@element-plus/icons-vue")['SwitchFilled']
export const LazyElIconTakeawayBox: typeof import("@element-plus/icons-vue")['TakeawayBox']
export const LazyElIconTicket: typeof import("@element-plus/icons-vue")['Ticket']
export const LazyElIconTickets: typeof import("@element-plus/icons-vue")['Tickets']
export const LazyElIconTimer: typeof import("@element-plus/icons-vue")['Timer']
export const LazyElIconToiletPaper: typeof import("@element-plus/icons-vue")['ToiletPaper']
export const LazyElIconTools: typeof import("@element-plus/icons-vue")['Tools']
export const LazyElIconTop: typeof import("@element-plus/icons-vue")['Top']
export const LazyElIconTopLeft: typeof import("@element-plus/icons-vue")['TopLeft']
export const LazyElIconTopRight: typeof import("@element-plus/icons-vue")['TopRight']
export const LazyElIconTrendCharts: typeof import("@element-plus/icons-vue")['TrendCharts']
export const LazyElIconTrophy: typeof import("@element-plus/icons-vue")['Trophy']
export const LazyElIconTrophyBase: typeof import("@element-plus/icons-vue")['TrophyBase']
export const LazyElIconTurnOff: typeof import("@element-plus/icons-vue")['TurnOff']
export const LazyElIconUmbrella: typeof import("@element-plus/icons-vue")['Umbrella']
export const LazyElIconUnlock: typeof import("@element-plus/icons-vue")['Unlock']
export const LazyElIconUpload: typeof import("@element-plus/icons-vue")['Upload']
export const LazyElIconUploadFilled: typeof import("@element-plus/icons-vue")['UploadFilled']
export const LazyElIconUser: typeof import("@element-plus/icons-vue")['User']
export const LazyElIconUserFilled: typeof import("@element-plus/icons-vue")['UserFilled']
export const LazyElIconVan: typeof import("@element-plus/icons-vue")['Van']
export const LazyElIconVideoCamera: typeof import("@element-plus/icons-vue")['VideoCamera']
export const LazyElIconVideoCameraFilled: typeof import("@element-plus/icons-vue")['VideoCameraFilled']
export const LazyElIconVideoPause: typeof import("@element-plus/icons-vue")['VideoPause']
export const LazyElIconVideoPlay: typeof import("@element-plus/icons-vue")['VideoPlay']
export const LazyElIconView: typeof import("@element-plus/icons-vue")['View']
export const LazyElIconWallet: typeof import("@element-plus/icons-vue")['Wallet']
export const LazyElIconWalletFilled: typeof import("@element-plus/icons-vue")['WalletFilled']
export const LazyElIconWarnTriangleFilled: typeof import("@element-plus/icons-vue")['WarnTriangleFilled']
export const LazyElIconWarning: typeof import("@element-plus/icons-vue")['Warning']
export const LazyElIconWarningFilled: typeof import("@element-plus/icons-vue")['WarningFilled']
export const LazyElIconWatch: typeof import("@element-plus/icons-vue")['Watch']
export const LazyElIconWatermelon: typeof import("@element-plus/icons-vue")['Watermelon']
export const LazyElIconWindPower: typeof import("@element-plus/icons-vue")['WindPower']
export const LazyElIconZoomIn: typeof import("@element-plus/icons-vue")['ZoomIn']
export const LazyElIconZoomOut: typeof import("@element-plus/icons-vue")['ZoomOut']
export const LazyNuxtPage: typeof import("../node_modules/nuxt/dist/pages/runtime/page")['default']
export const LazyNoScript: typeof import("../node_modules/nuxt/dist/head/runtime/components")['NoScript']
export const LazyLink: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Link']
export const LazyBase: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Base']
export const LazyTitle: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Title']
export const LazyMeta: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Meta']
export const LazyStyle: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Style']
export const LazyHead: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Head']
export const LazyHtml: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Html']
export const LazyBody: typeof import("../node_modules/nuxt/dist/head/runtime/components")['Body']

export const componentNames: string[]
