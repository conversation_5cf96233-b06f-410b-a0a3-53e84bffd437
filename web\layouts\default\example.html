<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>首页组件使用示例</title>
    <link rel="stylesheet" href="../../yibazhan/css/swiper.min.css"/>
    <link rel="stylesheet" href="../../yibazhan/css/animate.min.css"/>
    <link rel="stylesheet" href="../../yibazhan/css/style.css"/>
    <link rel="stylesheet" href="../../yibazhan/css/media.css"/>
    <link rel="stylesheet" href="../../yibazhan/css/scroll.css" />
    <script type="text/javascript" src="../../yibazhan/js/jquery.js"></script>
    <script type="text/javascript" src="../../yibazhan/js/scroll.js"></script>
    <script type="text/javascript" src="../../yibazhan/js/global.js"></script>
    <script type="text/javascript" src="../../yibazhan/js/swiper.min.js"></script>
    <script type="text/javascript" src="../../yibazhan/js/new_file.js"></script>
    <script type="text/javascript" src="../../yibazhan/js/jquery.SuperSlide.2.1.3.js"></script>
    <script type="text/javascript" src="../../yibazhan/js/wow.min.js"></script>
    <script>
        new WOW().init();
    </script>
    <style>
        .example-content {
            min-height: 600px;
            padding: 40px 0;
            background-color: #f8f9fa;
        }
        .example-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        .example-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .example-title {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
            text-align: center;
        }
        .example-subtitle {
            color: #555;
            margin-bottom: 15px;
            font-size: 1.8em;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
    </style>
</head>
<body oncontextmenu="doNothing()">
    <!-- 这里应该包含header组件的HTML内容 -->
    <!-- 由于内容较长，建议直接从homepage-header.vue中复制template部分 -->
    
    <!-- 页面主体内容 -->
    <div class="example-content">
        <div class="example-container">
            <h1 class="example-title">组件拆分完成</h1>
            
            <div class="example-section">
                <h2 class="example-subtitle">拆分结果</h2>
                <p>已成功将 <code>linggong/首页.html</code> 的顶部和底部拆分为独立的Vue组件：</p>
                <ul>
                    <li><strong>Header组件</strong>: <code>web/layouts/default/components/header/homepage-header.vue</code></li>
                    <li><strong>Footer组件</strong>: <code>web/layouts/default/components/footer/homepage-footer.vue</code></li>
                    <li><strong>布局组件</strong>: <code>web/layouts/default/homepage-layout.vue</code></li>
                    <li><strong>示例页面</strong>: <code>web/layouts/default/example-page.vue</code></li>
                </ul>
            </div>
            
            <div class="example-section">
                <h2 class="example-subtitle">组件特点</h2>
                <ul>
                    <li>保持了原始HTML的完整结构和功能</li>
                    <li>转换为Vue 3 Composition API格式</li>
                    <li>包含所有原始的CSS类名和JavaScript功能</li>
                    <li>支持响应式设计（PC端和移动端）</li>
                    <li>集成了客服系统和统计代码</li>
                </ul>
            </div>
            
            <div class="example-section">
                <h2 class="example-subtitle">使用说明</h2>
                <p>详细的使用说明请查看 <code>README.md</code> 文件。</p>
                <p>组件已经准备就绪，可以在Vue项目中直接使用。</p>
            </div>
        </div>
    </div>
    
    <!-- 这里应该包含footer组件的HTML内容 -->
    <!-- 由于内容较长，建议直接从homepage-footer.vue中复制template部分 -->
    
    <script type="text/javascript">  
        function doNothing(){  
            window.event.returnValue=false;  
            return false;  
        }  
    </script>
</body>
</html>
