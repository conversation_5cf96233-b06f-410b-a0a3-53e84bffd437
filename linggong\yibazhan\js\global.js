$(function() {
	var swiper01 = new Swiper('.clientswiper', {
		autoplay: {
		    delay: 5000,
		},
		loop: true,
		navigation: {
			nextEl: '.client_button_next',
			prevEl: '.client_button_prev',
		},
		breakpoints: { 
		    0: {
		        slidesPerView: 1,
		        spaceBetween: 20,
		    },
		    900: { 
		        slidesPerView: 3,
		        spaceBetween: 30,
		    },
		}
	});
	
	$(".clientswiper").mouseover(function(){
	    swiper01.autoplay.stop();
	});
	$(".clientswiper").mouseout(function(){
	    swiper01.autoplay.start();
	});
	
	var swiper02 = new Swiper('.certificate_swiper', {
		autoplay: {
		    delay: 000,
		},
		speed:4000,
		loop:true,
		allowTouchMove: false,
		slidesPerView: 4,
		grid: {
		  fill: 'column',
		  rows: 2,
	   	 },
		navigation: {
			nextEl: '.cert_button_next',
			prevEl: '.cert_button_prev',
		},
		breakpoints: {
		    0: {
		        slidesPerView: 2,
		        spaceBetween: 20,
		    },
		    900: { 
		        slidesPerView: 2,
		        spaceBetween: 35,
		    },
			1700: {
			    slidesPerView: 3,
			    spaceBetween: 25,
			},
		}
	});

    $(window).scroll(function(){
    	var navLeft = -$(window).scrollLeft();
    	$(".header").css("left",navLeft);
    });
    
    headerFade();
	function headerFade() {
	    var i = $(window).scrollTop();
	    $(window).scroll(function() {
	        if ($(this).scrollTop() > i && $(this).scrollTop() > 80) {
	            i = $(this).scrollTop();
	            $(".header").addClass("headerAct");
	        } else {
	            i = $(this).scrollTop();
	            $(".header").removeClass("headerAct");
	        }
	    });
	}
	
	$(".path_btn li").click(function(){
		$(".path_btn li").removeClass("path_btn_active");
		$(this).addClass("path_btn_active");
		var pIndex = $(this).index();
		$(".path .path_ul").hide();
		$(".path .path_ul").eq(pIndex).show();
	});
	
	$(".form_title li").click(function(){
		$(".form_title li").removeClass("form_title_on");
		$(this).addClass("form_title_on");
		var index1 = $(this).index();
		$(".form_ul li").hide();
		$(".form_ul li").eq(index1).show();
	});
	
	$("#opt01").click(function(){
	    var optIndex = $("#opt01 option:selected").val();
	    if(optIndex=="u01"){
	        $("#int01").hide();
	    }else if(optIndex=="u02"){
	        $("#int01").show();
	    }else{}
	    
	});
});
