*{ margin: 0; padding: 0; font-size: 16px; color: #222327; box-sizing: border-box;
font-family: -apple-system, BlinkMacSystemFont, "SF Pro SC", "SF Pro Text", "Helvetica Neue", Helvetica, "PingFang SC", "Segoe UI", Robot<PERSON>, "Hiragino Sans GB", 'arial', 'microsoft yahei ui', "Microsoft YaHei", SimSun, sans-serif;}
img{ border: 0; outline: 0; -ms-interpolation-mode: bicubic;}
div{ border: 0; outline: 0;}
a,p,span,h1,i{ text-decoration: none; margin: 0;}
li,ul{ list-style: none; cursor: pointer;}
input,button{ border: 0; outline: 0; background: none; margin: 0;}
a{cursor: pointer;}
a,ul,li,img,div{ -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
-webkit-user-select: none;
-moz-user-focus: none;
-moz-user-select: none;}
@font-face {
   font-family : pro-bold;
   src : url(../../yibazhan/font/../../yibazhan/font/../font/DINPro-Bold.otf); 
}
@font-face {
   font-family : mont-medium;
   src : url(../../yibazhan/font/../../yibazhan/font/../font/Montserrat-Medium.otf); 
}
@font-face {
	font-family : pro-black;
	src : url(../../yibazhan/font/../../yibazhan/font/../font/DINPro-Black.otf);
}
#backup{ display:none;}
.header{ position: fixed; width: 100%; top: 0; left: 0; z-index: 99999; border-bottom: solid 1px #d4dae3; transition: background 0.5s, transform 0.5s; opacity: 1; visibility: visible;}
.header .header_logo{ float: left; overflow: hidden;}
.header_logo img{ float: left;}
.header_logo span{ float: left; border-left: solid 1px #d4dae3;}
.header_pc{ float: left;}
.header_pc ul::after{ content:''; display:block; width: 0; height:0; clear:both; visibility:hidden;}
.header_pc .header_topic{ float: left; cursor: pointer; position: relative;}
.header_word01{ transition: all 0.3s; position: relative;}
.header_word01 a{ display: inline-block;}
.header_word02{ position: absolute; left: 50%; z-index: 99999; padding: 10px 0; min-width: 130%; box-shadow: 0 1px 3px #e1e1e1;
opacity: 0; visibility: hidden; transition: all 0.6s; transform: translate(-50%, -5%); border-radius: 0.2em;}
.header_word02::after{ content:''; position: absolute; top: -0.7em; left: 50%; transform: translateX(-50%); z-index: 100; border-radius: 0.2em;
width: 0; height: 0; border-left: solid 0.6em transparent; border-right: solid 0.6em transparent; border-bottom: solid 0.8em #fff;}
.header_word02 a{ display: block; color: #222; font-size: 0.875em; line-height: 3em; white-space: nowrap; text-align: center; padding: 0 20px;}
.header_topic:hover .header_word02{ opacity: 1; visibility: visible; background: #fff; transform: translate(-50%,0);}
.header_word02 a:hover{ background: #336de2; color: #fff;}
.login{ text-align: center; border-left: solid 1px #d4dae3;}
.login span{ cursor: pointer;}
.login i{ font-style: normal;}
.header_tip{ float: right;}
.header_tip::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.tell_tu,.code_tu,.earth_tu,.searcher{ float: right; cursor: pointer; position: relative;}
.tell_tu img,.code_tu img{ display: block;}
.tell_tu::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.code_tu::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.earth_tu::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.earth_tu img{ float: left;}
.earth_tu span{ display: inline-block;}
.searcher{ display: inline-flex; align-items: center; border-left: solid 1px #d4dae3; border-right: solid 1px #d4dae3;}
.searcher input::-webkit-input-placeholder { color: #acb0bd;}
.searcher input:-moz-placeholder { color: #acb0bd;}
.searcher input::-moz-placeholder { color: #acb0bd;}
.searcher input::-ms-input-placeholder { color: #acb0bd;}
.tip_box{ opacity: 0; visibility: hidden; background: #fff; position: absolute; left: 50%; transform: translateX(-50%); z-index: 99;}
.tip_box::after{ content:''; position: absolute; top: -0.7em; left: 50%; transform: translateX(-50%); z-index: 100; border-radius: 0.2em;
width: 0; height: 0; border-left: solid 0.6em transparent; border-right: solid 0.6em transparent; border-bottom: solid 0.8em #fff;}
.code_pic{ display: block;}
.tip_box section{ font-weight: bold; color: #336de2;}
.headerAct{ -webkit-transform: translateY(-100%); transform: translateY(-100%);}
.tip_box1{ opacity: 0; visibility: hidden; background: #336de2; position: absolute; left: 50%; transform: translateX(-50%); z-index: 99;}
.tip_box1::after{ content:''; position: absolute; top: -0.7em; left: 50%; transform: translateX(-50%); z-index: 100; border-radius: 0.2em;
width: 0; height: 0; border-left: solid 0.6em transparent; border-right: solid 0.6em transparent; border-bottom: solid 0.8em #336de2;}
.tip_box1 section{ font-weight: bold; color: #fff;white-space: nowrap;}
.tell_tu:hover>.tip_box1{ transition: all 0.3s; opacity: 1; visibility: visible;}
.code_tu:hover>.tip_box{ transition: all 0.3s; opacity: 1; visibility: visible;}
.header_bg{ background: #fff;}
.world{ background: #fff; width: 100%; position: fixed; left: 0; z-index: 20; transition: all 0.3s; opacity: 0; visibility: hidden;}
.world span{ display: block; color: #336de2;}
.world img{ position: absolute; bottom: 0; z-index: 100;}
.earth_tu:hover+.world{ opacity: 1; visibility: visible;}
.header_title{ position: fixed; width: 100%; left: 0; top: 0; z-index: 999999; background: #336de2;
opacity: 0; visibility: hidden; transition: all 0.6s;}
.header_title01{ padding: 1rem 8%; border-bottom: solid 1px #497de5; overflow: hidden; line-height: 1.2rem;}
.header_title01 p{ color: #fff; font-weight: bold; float: left; font-size: 0.8rem;}
.header_title01 span{ color: #fff; font-weight: bold; float: right; font-size: 0.9rem; padding: 0 0 0.15rem 0.3rem;}
.header_title02{ max-height: 80vh; overflow-y: scroll;}
.dropdown-menu{ border-bottom: solid 1px #497de5;}
.dropdown-menu p{ font-size: 0.7rem; color: #fff; padding: 0 8%; line-height: 2.2rem;}
.dropdown-menu ul{ display: flex; align-items: center; flex-wrap: wrap; padding: 0.3rem 0 0.3rem 8%; background: #457ef0;}
.dropdown-menu li{ line-height: 1.8rem; height: 1.8rem;} 
.dropdown-menu .dropdown-ul li{ width: 49%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #fff; text-align: left;}
.dropdown-menu li a{ font-size: 0.65rem; color: #fff;}
.dropdown-menu .dropdown-ul .fullwide{ width: 100%;}
.header_menu img{ display: block;}
.title_show{ opacity: 1; visibility: visible;}
.banner{ width: 100%; position: relative;}
.banner_pic{ width: 100%; overflow: hidden;}
.banner_bg{ background: #1b2b6b; position: relative;}
.banner_bg img{ position: absolute; top: 55%; transform: translateY(-50%); z-index: 9;}
.banner_word{ position: absolute; top: 50%; transform: translateY(-50%); z-index: 99;}
.banner_word p{ color: #fff; font-family: "Source Han Sans CN Medium", "Noto Sans Medium", sans-serif;}
.banner_word span{ display: block; color: #fff; font-family: "Source Han Sans CN Regular", "Noto Sans Regular", sans-serif;}
.banner_word button{ color: #fff; border: solid 1px #fff; text-align: center; cursor: pointer; transition: all 0.3s; font-family: "Source Han Sans CN Regular", "Noto Sans Regular", sans-serif;}
.banner_word button:hover{ border: solid 1px #336de2; background: #336de2; color: #fff;}
.banner_form{ background: #fff;}
.form_title{ position: relative;}
.form_title li{ position: absolute; bottom: 0; z-index: 99; width: 50%; text-align: center; background: linear-gradient(to right, #336de2,#2d4fd4);}
.form_title li p{ display: inline-block; color: #fff;}
.form_title li:first-child{ left: 0;}
.form_title li:last-child{ right: 0;}
.form_title .form_title_on{ background: #fff;}
.form_title .form_title_on p{ color: #333; position: relative;}
.form_title .form_title_on p::after{ content: ''; position: absolute; bottom: 0; left: 0; z-index: 99; background: #c8d1eb;}
.edit_text{ position: relative; background: #f0f2f6; }
.edit_text1 img{ position: absolute; top: 50%; left: 5%; transform: translateY(-50%); z-index: 9;}
.edit_text2 span{ color: #7f7f7f; position: absolute; top: 50%; transform: translateY(-50%); z-index: 9;}
.edit_text2 .name{ left:5%; right: auto; z-index: 9; }
.form_ul button{ width: 100%; background: linear-gradient(to right, #336de2,#2d4fd4); color: #fff; cursor: pointer;}
.form_ul button:hover{ background: #2d4fd4;}
.edit_tip1{ color: #81848f; text-align: center;}
.edit_tip1 span{ color: #336de2; font-weight: bold;}
.edit_tip2{ background: #e8f2fc; text-align: center;}
.edit_tip2 span{ color: #336de2; font-weight: bold;}
.form_ul li{ display: none;}
.form_ul .form_ul_show{ display: block;}
.banner_adv{ width: 100%;}
.banner_adv ul{ width: 100%; display: flex; justify-content: space-between; overflow: hidden; background: #fff;}
.banner_adv li{ overflow: hidden;}
.banner_adv li img{ float: left;}
.banner_adv li section{ float: left;}
.banner_adv p{ font-weight: bold; color: #000;}
.banner_adv span{ display: block; color: #7b7e88;}
.container{ margin: 0 auto;}
.container::after{ content:''; display:block; width: 0; height:0; clear:both; visibility:hidden;}
.rel{ position: relative;}
.flow{ overflow: hidden;}
.clear{ clear: both;}
.fl{ float: left;}
.fr{ float: right;}
.wide_all{ width: 100%;}
.product_title{ position: relative;}
.product_title h2{ font-weight: normal; color: #000; text-align: center;}
.product_title .product_col{ color: #fff;}
.product_bg01{ background: #f3f4f8; }
.product_bg66{ background: url(../../yibazhan/images/../../yibazhan/images/../img/Lhyg_kpbg.jpg) center no-repeat; background-size: cover; }
.product_title .product_tip01{ color: #7b7e88;}
.pro_line{ display: block; background: #336de2;}
.serve{ overflow: hidden; position: relative;}
.serve_ul{ overflow: hidden;}
.serve_ul li{ float: left; border-right: solid 1px #e1e1e1; border-bottom: solid 1px #e1e1e1; transition: all 0.3s; position: relative; cursor: pointer;}
.serve_ul p{ color: #7b7e88;}
.serve_ul .serve_img{ position: absolute; z-index: 9;}
.serve_img img{ display: block;}
.serve_img .serve_img02{ display: none;}
.serve_ul .serve_icon{ transition: all 0.5s;}
.serve_ul li:hover{ background: #f3f6fa;}
.serve_ul li:hover .serve_img01{ display: none;}
.serve_ul li:hover .serve_img02{ display: block;}
.serve_ul li:hover .serve_icon{ transform: translateX(8px);}
.serve_db{ background: #f1f3f6;}
.serve_db1{ position: relative; overflow: hidden;}
.serve_db1 h1{ float: left;}
.serve_db1 a{ color: #7b7e88; position: absolute; bottom: 0; right: 0; z-index: 9;}
.serve_db1 a:hover{ color: #336de2;}
.serve_db2{ border: solid 1px #e1e1e1; background: #fff; position: relative;}
.serve_db2 input{ width: 85%;}
.serve_db2 img{ position: absolute; right: 7%; top: 50%; transform: translateY(-50%); z-index: 50;}
.serve_db3 li{ border-bottom: dashed 1px #dadde5; color: #7b7e88; position: relative;}
.serve_db3 li::before{ content: ''; position: absolute; left: 0; top: 50%; transform: translateY(-50%); z-index: 90; border-radius: 50%; background: #cacfda;}
.serve_db3 li:last-child{ border-bottom: 0;}
.serve_db3 li:hover{ color: #333;}
.solve_bg{ background: linear-gradient(to bottom, #31374c,#525c78);}
.solve_line{ width: 100%; height: 1px; background: #535b75; position: absolute; left: 0; z-index: 9;}
.solve{ overflow: hidden;}
.solve_title{ float: left; background: #3f455c;}
.solve_title .solve_item{ overflow: hidden; border-bottom: solid 1px #4d5473;}
.solve_title li:last-child .solve_item{ border-bottom: 0;}
.solve_title .on{ background: #336de2;}
.solve_title .solve_icon02{ display: none;}
.solve_title img{ float: left;}
.solve_title p{ float: left; color: #fff;}
.solve_title .on .solve_icon02{ display: block;}
.solve_title .on .solve_icon01{ display: none;}
.solve_con{ float: right;}
.solve_infor11{ font-weight: normal; color: #fff;}
.solve_infor1 span{ display: inline-block; background: #336de2;}
.solve_infor1 p{ color: #c6cee6;}
.solve_infor2{ position: relative;}
.solve_infor2 p{ display: inline-block; color: #fff;}
.solve_infor2 span{ display: inline-block; background: #fff; color: #6b6e79;}
.solve_infor2 a{ display: inline-block; background: #3f455c; color: #fff; transition: all 0.2s; position: absolute; z-index: 99;}
.solve_infor2 a:hover{ background: #336de2;}
.news{ overflow: hidden;}
.news_a{ float: left; position: relative;}
.news_b{ float: left;}
.news_c{ float: left; position: relative;}
.news_title{ position: relative;}
.news_bor{ border-top: solid 1px #c8c8c8;}
.news_title h3,.news_title h3{ display: inline-block; color: #000; position: relative;}
.news_title h3::after{ content: ''; position: absolute; left: 0; width: 100%; background: #336de2;}
.news_title a{ display: inline-flex; align-items: center; position: absolute; right: 0; top: 50%; transform: translateY(-50%); z-index: 99; transition: all 0.2s;}
.news_title span{ color: #7b7e88;}
.news_title .news_title_icon02{ display: none;}
.news_title a:hover>span{ color: #336de2;}
.news_title a:hover>.news_title_icon02{ display: inline-block;}
.news_title a:hover>.news_title_icon01{ display: none;}
.news_a_item{ overflow: hidden; position: relative;}
.news_a_pic{ float: left; overflow: hidden;}
.news_a_pic img{ width: 100%; height: 100%; object-fit: cover; transition: all 0.5s;}
.news_a_w{ float: right; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; transition: all 0.2s;}
.news_a_t{ color: #a2a2a2; position: absolute; right: 0; bottom: 0; z-index: 9; transition: all 0.2s;}
.news_a_item:hover .news_a_pic img{ transform: scale(1.08);}
.news_a_item:hover .news_a_w{ font-weight: bold;}
.news_a_item:hover .news_a_t{ color: #333;}
.news_tab p{ position: relative; cursor: pointer;}
.news_tab .on{ font-weight: bold;}
.news_tab .on::after{ content: ''; position: absolute; top: -0.1em; left: 0; z-index: 9; width: 100%; height: 0.3em; background: #336de2;}
.news_b_list1{ font-weight: bold; text-overflow: ellipsis; white-space: nowrap; overflow: hidden; transition: all 0.2s; cursor: pointer;}
.news_b_list1:hover{ color: #336de2;}
.news_b_list2w{ position: relative;}
.news_b_list2w::before{ content: ''; position: absolute; left: 0; top: 50%; transform: translateY(-50%); z-index: 90; border-radius: 50%; background: #cacfda;}
.news_b_list2w p{ color: #414141; text-overflow: ellipsis; white-space: nowrap; overflow: hidden; transition: all 0.2s;}
.news_b_list2w span{ color: #a2a2a2; position: absolute; top: 50%; transform: translateY(-50%); right: 0; z-index: 9; transition: all 0.2s;}
.news_b_list2w:hover>p{ font-weight: bold;}
.news_b_list2w:hover>span{ color: #333;}
.news_c{ border: solid 1px #c8c8c8;}
.news_c_list li{ border-bottom: dashed 1px #c8c8c8;}
.news_c_list li:last-child{ border-bottom: 0;}
.news_c_list span{ font-weight: bold; font-style: oblique; display: inline-block; color: #7b7e88; transition: all 0.2s;}
.news_c_list p{ display: inline-block; color: #7b7e88; transition: all 0.2s;}
.news_c_list li:hover>span{ color: #333; font-weight: bold;}
.news_c_list li:hover>p{ color: #333; font-weight: bold;}
.support{ background: #fff;}
.support ul{ overflow: hidden;}
.support li{ float: left;}
.support1{ border-bottom: solid 1px #dadde5; position: relative;}
.support li .support1:last-child{ border-bottom: 0;}
.support1 .support_icon span{ color: #7b7e88; transition: all 0.2s;}
.support1 .support_icon .support_icon02{ display: none;}
.support1 .support_pic{ position: absolute; right: 8%; top: 50%; transform: translateY(-50%); z-index: 9;}
.support2{ position: relative;}
.support2 span{ display: inline-block; background: #336de2;}
.support2 p{ color: #7b7e88;}
.support2_btn button{ cursor: pointer; transition: all 0.2s;}
.support2_btn .support2_btn01{ border: solid 1px transparent; background: #edeff3; color: #5c6172;}
.support2_btn .support2_btn02{ border: solid 1px #d8dbe6; color: #5c6172;}
.support1:hover .support_icon span{ color: #336de2;}
.support1:hover .support_icon .support_icon01{ display: none;}
.support1:hover .support_icon .support_icon02{ display: inline-block;}
.support2_btn button:hover{ background: #336de2; color: #fff;}
.partner{ overflow: hidden;}
.partner1{ float: left;}
.partner2{ float: right;}
.partner1 li{ overflow: hidden;}
.partner1 li:last-child{ margin-bottom: 0;}
.partner11{ font-weight: normal; color: #336de2;}
.partner1 p{ color: #74767a;}
.partner2 ul{ overflow: hidden;}
.partner2 li{ float: left;}
.touch_bg{ background: url(../../yibazhan/images/../../yibazhan/images/../img/touch_pic.jpg) center no-repeat; background-size: cover;}
.touch{ width: 100%; position: relative;}
.touch h5{ font-weight: normal; color: #fff;}
.touch p{ color: #fff;}
.touch button{ position: absolute; right: 0; top: 50%; transform: translateY(-50%); z-index: 99; color: #fff; background: linear-gradient(to right, #336de2,#2d4fd4); cursor: pointer;}
.touch button:hover{ background: #2d4fd4;}
.footer{ background: #292d3f;}
.footer01,.footer02{ border-bottom: solid 1px #3d4460;}
.footer01 ul{ display: flex; justify-content: space-between;}
.footer01 li{ display: inline-flex;}
.footer01 section{ display: inline-flex; flex-direction: column;}
.footer01 h6{ font-weight: normal; color: #fff;}
.footer01 p{ color: #999fb1;}
.footer02{ overflow: hidden;}
.footer02a{ float: left;}
.footer02a ul{ overflow: hidden;}
.footer02a li{ float: left;}
.footer02a p{ color: #fff;}
.footer02a span{ display: block; color: #999fb1;}
.footer02a span:hover{ color: #fff;}
.footer02b{ float: right;}
.footer02b h6{ font-weight: normal; color: #fff;}
.footer02b p{ color: #999fb1;}
.footer02b_img{ overflow: hidden;}
.footer02b_img img{ float: left;}
.footer02b span{ color: #fff;}
.footer03a{ position: relative; overflow: hidden;}
.footer03a img{ float: left;}
.footer03a span{ float: left; color: #999fb1; position: relative; cursor: pointer;}
.footer03a span:hover{ color: #fff;}
.footer03a select{ position: absolute; right: 0; top: 50%; transform: translateY(-50%); z-index: 99;
background: linear-gradient(to bottom, #4f556f,#5e657f); color: #fff; border: 0; outline: 0; cursor: pointer;}
.footer03a span::after{ content: ''; display: inline-block; position: absolute; top: 25%; right: 0; z-index: 9; 
width: 1px; height: 50%; background: #999fb1;}
.footer03a span:last-child{ margin-right: 0; padding-right: 0;}
.footer03b{ overflow: hidden;}
.footer03b_l{ float: left;}
.footer03b_l p{ color: #999fb1;}
.footer03b_r{ float: right;} 
.picture{ width: 100%; position: relative;}
.picture_pic{ width: 100%; overflow: hidden;}
.picture_pic img{ width: 100%; height: 100%; object-fit: cover;}
.picture_word{ position: absolute; z-index: 99;}
.picture_word p{ color: #fff; font-family: "Source Han Sans CN Medium", "Noto Sans Medium", sans-serif;}
.picture_word span{ display: block; font-family: mont-medium; color: #fff; text-transform: uppercase;}
.picture_word section{ color: #fff; font-family: "Source Han Sans CN Regular", "Noto Sans Regular", sans-serif;line-height:2em}
.brief_general::after{ content:''; display:block; width: 0; height:0; clear:both; visibility:hidden;}
.brief_l{ float: left;}
.brief_r{ float: right;}
.brief_h1{ color: #336de2; font-weight: bold;}
.brief_word{ color: #787b85;}
.brief_btn button{ background: #79a0f0; box-shadow: 0 2px 5px #6395fb; color: #fff;}
.brief_word span{color:#336de2; font-size:1.2em; font-weight:bold; line-height:3em; display:block;  }
.hdzs{padding-right:10%;}
.brief_l  span{color:#336de2; font-size:1.2em; font-weight:bold; line-height:3em; display:block;}
.brief_r  b{color:#336de2; font-size:1.2em; font-weight:bold; line-height:3em; padding-bottom:1em; display:block;}
.brief_r dl{ width:100%; display:flex; justify-content:space-between; flex-flow:wrap row; box-sizing:border-box; border-bottom:1px #ced1da solid; font-size:0.9em; }
.brief_r dl:nth-child(odd){background:#fff;}
.brief_r dl:nth-child(even){background:#f6f6f6;}
.brief_r dl.title{ border-top:1px #ced1da solid; background:#ecf0f8;}
.brief_r dl dt{width:16%; padding:1em; border-right:1px #ced1da solid;  border-left:1px #ced1da solid;font-size:0.9em;}
.brief_r dl dd{width:21%; padding:1em; border-right:1px #ced1da solid;font-size:0.9em; }
.brief_r dl span{ color:#336de2; font-weight:normal;font-size:0.9em; padding:0;line-height:1em}
.question ul,.questioner ul{ overflow: hidden; display: flex; justify-content: space-between;}
.question li,.questioner li{ background: #fff; transition: all 0.3s;}
.question li:hover{ background: #fcfcfc;}
.questioner li:hover{ background: #fcfcfc;}
.question_title{ overflow: hidden;}
.question_title img{ float: left;}
.question_title p{ float: left; font-weight: bold;}
.question_caption p{ font-weight: bold;}
.question section,.questioner section{ color: #7b7e88;}
.question_caption .caption_right{ text-align: right;}
.plan_t{ font-weight: bold;}
.plan_ul{ overflow: hidden;}
.plan_ul li{ float: left;}
.plan_ul span{ display: inline-block; color: #336de2; border: solid 1px #336de2;}
.plan_ul p{ color: #7b7e88;}
.plan_pic,.plan_pic1{ width: 100%;}
.plan_pic img,.plan_pic1 img{ display: block; width: 100%;}
.plan_h{ text-align: justify;}
.plan_p1{ position: relative; font-weight: bold;}
.plan_p1::after{ content: ''; position: absolute; left: 0; top: 12%; z-index: 9; background: #336de2; height: 80%;}
.plan_p2{ color: #7b7e88;}
.product_btn{ position: absolute; bottom: 0; right: 0; z-index: 99; background: #79a0f0; color: #fff; box-shadow: 0 0 10px #9cbaf6;}
.analytical ul.detail_tab{ width:100%; display:flex; justify-content:space-between; flex-wrap:nowrap;}
ul.detail_tab li{ padding:1em 2em; width:100%; font-size:0.825em; line-height:2em; box-sizing:border-box; border:1px #ededed solid; background:#fff;}
ul.detail_tab li:nth-child(1){width:12%;}
ul.detail_tab li:nth-child(2){width:12%;}
ul.detail_tab li:nth-child(3){width:38%;}
ul.detail_tab li:nth-child(4){width:38%;}

.analytical ul.detail_tab2{ width:100%; display:flex; justify-content:space-between; flex-wrap:nowrap;}
ul.detail_tab2 li{ padding:1em 2em; width:100%; font-size:0.825em; line-height:2em; box-sizing:border-box; border:1px #ededed solid; background:#fff;}
ul.detail_tab2 li:nth-child(1){width:30%;}
ul.detail_tab2 li:nth-child(2){width:70%;}


ul.lhyg_yt{ width:100%; display:flex; flex-wrap:wrap; justify-content:space-between; margin-bottom:40px;}
ul.lhyg_yt li{width:18%; background:#fff; box-sizing:border-box; padding:20px; font-size:18px; line-height:24px; margin-top:20px; border-radius:6px;}
ul.lhyg_yt li p{ font-size:12px; color:#ccc; white-space:nowrap;}




.analytical_ul{ overflow: hidden;} 
.analytical_ul li{ float: left; background: #fff;}
.analytical_ul_other{ overflow: hidden;}
.analytical_ul_other li{ float: left; background: #fff;}
.analytical_line{ border-bottom: solid 1px #e6e8ec;}
.analytical_ul1 button{ background: linear-gradient(to right,#898e9e,#6c7181); color: #fff; box-shadow: 0 2px 5px #9a9da4;}
.analytical_ul2 button{ background: linear-gradient(to right,#336be1,#2d4fd4); color: #fff; box-shadow: 0 2px 5px #5f7ae1;}
.analytical_ul3 button{ background: linear-gradient(to right,#6c97ee,#94b4f5); color: #fff; box-shadow: 0 2px 5px #a0bbf2;}
.analytical_ul1 p,.analytical_ul2 p{ color: #7b7e88;}
.analytical_ul2 span{ color: #336de2;}
.analytical_btn{ text-align: center;}
.analytical_btn p{ display: inline-block; background: #fff;}
.analytical_btn span{ color: #336de2; font-weight: bold;}
.analytical_line .an_br{ display: block;}
.analytical_line .an_hue{ color: #333;}
.analytical_item p{ position: relative;}
.analytical_item p::after{ content: ''; position: absolute; left: 0; z-index: 9; border-radius: 50%; background: #336de2;}
.analytical_account{ overflow: hidden;}
.analytical_account1{ float: left;}
.analytical_account2{ float: right;}
.analytical_account_h{ overflow: hidden;}
.analytical_account_h p{ color: #7b7e88; float: left;}
.analytical_account_h span{ float: right;}
.analytical_account_h .account_col{ color: #e75263;}
.analytical_line .account_col{ color: #e75263;}
.analytical_th{ overflow: hidden;}
.analytical_th p{ float: left;}
.analytical_th section{ float: right; color: #7b7e88;}
.analytical_th span{ float: right;}
.virtue ul{ overflow: hidden;}
.virtue li{ float: left; overflow: hidden;}
.virtue li:last-child{ margin-right: 0;}
.virtue .virtue_pic{ float: left;}
.virtue .virtue_word{ float: left;}
.virtue_word p{ font-weight: bold;}
.virtue_word span{ color: #7b7e88;}
.client{ background-image: url(../../yibazhan/images/../../yibazhan/images/../img/quote_icon.png); background-repeat: no-repeat; border: solid 1px #e1e1e1;}
.client_button{ overflow: hidden; position: absolute; right: 0; bottom: 0; z-index: 99;}
.client_button img{ display: block;}
.client_button .client_button_img02{ display: none;}
.client_button_prev{ float: left; cursor: pointer;}
.client_button_next{ float: right; cursor: pointer;}
.client_title{ overflow: hidden; display: flex; align-items: center; justify-content: space-between;}
.client_title p{ font-weight: bold;}
.client_word p{ color: #000; display: -webkit-box; -webkit-box-orient: vertical; overflow: hidden;} 
.client_button_prev:hover .client_button_img01{ display: none;}
.client_button_prev:hover .client_button_img02{ display: block;}
.client_button_next:hover .client_button_img01{ display: none;}
.client_button_next:hover .client_button_img02{ display: block;}
.pro_form{ background: linear-gradient(to bottom, #172a65,#1f3778); overflow: hidden;}
.pro_form_title h5{ color: #fff; font-weight: normal; display: inline-block;}
.pro_form_title p{ color: #fff; letter-spacing: 1px;}
.pro_form_input{ overflow: hidden;}
.pro_form_input input{ float: left; background: #2e478b;}
.pro_form_input input::-webkit-input-placeholder { color: #a7b1cb;}
.pro_form_input input:-moz-placeholder { color: #a7b1cb;}
.pro_form_input input::-moz-placeholder { color: #a7b1cb;}
.pro_form_input input::-ms-input-placeholder { color: #a7b1cb;}
.pro_form_input button{ float: left; background: linear-gradient(to bottom, #336de2,#2d50d5); color: #fff;}
.pro_form_pic{ position: absolute; right: 0; z-index: 9;}
.plan_chart table{ width: 100%; background: #ccc; border: 0; text-align: center;}
.plan_chart th{ background: #fff; word-break: break-word;}
.plan_chart td{ background: #fff; word-break: break-word;}
.plan_chart_title td{ color: #7b7e88;}
.plan_chart_b{ font-weight: bold;}
.plan_chart .green{ color: #00b56b;}
.plan_chart .blue{ color: #336de2;}
.brief_pic ul::after{ content:''; display:block; width: 0; height:0; clear:both; visibility:hidden;}
.brief_pic li{ float: left; text-align: center;}
.brief_pic li img{ transition: all 0.9s;}
.brief_pic li:hover>img{ transform: rotateY(360deg);}
.brief_pic1 li{ background: linear-gradient(to bottom, #f9fafd,#f0f2f7); border: solid 1px #e1e1e1;}
.brief_pic2 li{ background: linear-gradient(#95b5f5, #6c97ee); box-shadow: 0 0 20px #e6e6e6;}
.brief_pic3 li{ background: linear-gradient(#fcfcfd,#f0f1f5); box-shadow: 0 0 20px #e7e7ef; border: solid 1px #e1e1e1; transition: all 0.1s;}
.brief_ot_2 li{ position: relative;}
.brief_ot_2 li::after{ content: ''; position: absolute; left: 0; z-index: 9; border-radius: 50%; background: #336de2;}
.brief_ot_1{ font-weight: bold;}
.brief_ot_2 li{ color: #787b85;}
.brief_pic2 li p{ color:#fff;}
.brief_pic3 li:hover>p{ color: #fff;}
.brief_pic3 .brief_pic3-a:hover{ background: url(../../yibazhan/images/../../yibazhan/images/../../img/b_icon101b-bg.jpg) center no-repeat; background-size: cover;}
.brief_pic3 .brief_pic3-b:hover{ background: url(../../yibazhan/images/../../yibazhan/images/../../img/b_icon12b-bg.jpg) center no-repeat; background-size: cover;}
.brief_pic3 .brief_pic3-c:hover{ background: url(../../yibazhan/images/../../yibazhan/images/../../img/b_icon103b-bg.jpg) center no-repeat; background-size: cover;}
.brief_pic3 .brief_pic3-d:hover{ background: url(../../yibazhan/images/../../yibazhan/images/../../img/b_icon104b-bg.jpg) center no-repeat; background-size: cover;}
.adapt ul{ overflow: hidden;}
.adapt li{ float: left; border: solid 1px #e1e1e1; position: relative;}
.adapt_word{ position: absolute; z-index: 99;}
.adapt span{ display: inline-block; background: #336de2;}
.adapt_pic{ position: absolute; top: 0; left: 0; z-index: 9; width: 100%; height: 100%; overflow: hidden;
opacity: 0; visibility: hidden; transition: all 0.3s;}
.adapt_pic img{ width: 100%; height: 100%; object-fit: cover;}
.adapt_icon{ position: absolute; z-index: 99;}
.adapt_icon img{ display: block;}
.adapt_icon .adapt_icon02{ display: none;}
.adapt li:hover .adapt_pic{ opacity: 1; visibility: visible;}
.adapt li:hover .adapt_icon01{ display: none;}
.adapt li:hover .adapt_icon02{ display: block;}
.adapt li:hover h1{ color: #fff;}
.path ul{ display: flex; flex-wrap: wrap;}
.path li{ overflow: hidden;}
.path p{ float: left; font-family: pro-bold; color: #336de2;}
.path section{ float: left;}
.path span{ display: block; color: #7b7e88; text-transform: uppercase; word-break: break-all;}
.path h2{ color: #7b7e88; font-weight: normal; text-align: justify;}
.path .path_h{ display: none;}
.brief_card ul{ display: flex;}
.brief_card li{ overflow: hidden; background: #fff; box-shadow: 0 0 20px #e1e1e1; position: relative;}
.policy ul{ overflow: hidden; border: solid 1px #e1e1e1;}
.policy li{ float: left;}
.policyy{ position: relative;}
.policy p{ color: #7b7e88; position: relative;}
.policyy::after{ content: ''; position: absolute; left: 0; z-index: 9; background: #336de2;}
.policy p::after{ content: ''; position: absolute; left: 0; z-index: 9; border-radius: 50%; background: #336de2;}
.theory{ width: 100%;}
.theory img{ display: block; width: 100%;}
.scene ul{ overflow: hidden;}
.scene li{ float: left; overflow: hidden;}
.scene img{ float: left;}
.scene span{ float: left;}
.receipt ul::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.receipt li{ float: left; box-shadow: 0 0 20px #e7e9ed;}
.receipt li:nth-child(1){ background: #fff;}
.receipt li:nth-child(2){ background: #79a0f0; color: #fff;}
.receipt-bill ul::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.receipt-bill li{ float: left; background: #fff; box-shadow: 0 0 20px #dadeec; text-align: center; transition: all 0.2s;}
.receipt-bill li:hover{ background: #6c97ee; color: #fff;}
.path_btn ul{ overflow: hidden; border-bottom: solid 1px #e1e1e1;}
.path_btn li{ float: left; color: #787b85; border-bottom: solid 3px transparent;}
.path_btn .path_btn_active{ color: #333; border-bottom: solid 3px #336de2; font-weight: bold;}
.trade ul{ overflow: hidden;}
.trade li{ float: left; border: solid 1px #e1e1e1; transition: all 0.1s;}
.trade_pic{ width: 100%; position: relative;}
.trade li .trade_icon01{ position: absolute; z-index: 9;}
.trade_icon01 img{ display: block; transition: all 0.3s;}
.trade_icon01 .trade_icon01b{ display: none;}
.trade01 li .trade_icon02{ position: absolute; z-index: 9;}
.trade_icon02 img{ display: block;}
.trade_icon02 .trade_icon02b{ display: none;}
.trade01 li:hover{ background: #336de2;}
.trade01 li:hover p{ color: #fff;}
.trade01 li:hover .trade_icon01a{ display: none;}
.trade01 li:hover .trade_icon01b{ display: block;}
.trade01 li:hover .trade_icon02a{ display: none;}
.trade01 li:hover .trade_icon02b{ display: block;}
.trade02 .trade_pic p{ font-weight: bold;}
.trade02 .trade_pic section{ color: #7b7e88;}
.trade02 li:hover .trade_icon01 img{ transform: rotateY(180deg);}
.guest{ overflow: hidden;}
.guest_title li{ color: #787b85; border-bottom: solid 3px transparent;}
.guest_title .on{ color: #333; font-weight: bold; border-bottom: solid 3px #336de2;}
.guest_ul li{ overflow: hidden;}
.guest_pic{ float: left; background: #fff; text-align: center;}
.guest_pic img{ vertical-align: middle;}
.packet_btn{ color: #7b7e88; border: solid 1px #bcbcbc; position: absolute; right: 0; bottom: 0; z-index: 99; transition: all 0.2s; cursor: pointer;}
.packet_btn:hover{ background: #336de2; color: #fff; border: solid 1px #336de2;}
.clause ul{ overflow: hidden;}
.clause li{ float: left; overflow: hidden;}
.clause_pic{ overflow: hidden; float: left;}
.clause_pic img{ width: 100%; height: 100%; object-fit: cover; transition: all 0.3s;}
.clause_word{ float: right; position: relative;}
.clause_word h1{ display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden;}
.clause_word p{ color: #7b7e88; position: absolute; left: 0; bottom: 0; z-index: 9;}
.clause li:hover .clause_pic img{ transform: scale(1.05);}
.tax-ul ul{ overflow: hidden;}
.tax-ul li{ float: left;}
.tax-title{ overflow: hidden;}
.tax-title img{ float: left;}
.tax-titlee{ float: left;font-weight:bold;}
.tax-p{ color: #787b85;}
.preference ul::after{ content:''; display:block; height:0; clear:both; visibility:hidden;}
.preference li{ float: left; background: #fff; box-shadow: 0 0 20px #e3e3e3;}
.preference1{ position: relative; border-bottom: solid 1px #e9ecf2;}
.preference1 button{ background: linear-gradient(to right,#336ce2,#2d4fd4); color: #fff; box-shadow: 0 0 5px #758be1;}
.grey button{ background: linear-gradient(to right,#898e9e,#6c7181); color: #fff; box-shadow: 0 0 5px #9a9da4;}
.preference1 span{ color: #eff1f6; font-family : pro-black; position: absolute; top: 50%; transform: translateY(-50%); z-index: 9;}
.preference2 p{ color: #787b85;}
.resource_bg{ background: linear-gradient(to bottom,#31374c,#535d79);}
.resource_l{ float: left;}
.resource_w p{ color: #fff;}
.resource_w section{ color: #fff;}
.resource_r img{ display: block; width: 100%;}
.principle{ overflow: hidden;}
.principle_l{ float: left; background: #fff;}
.principle_r{ float: right;}
.principle_r img{ width: 100%; display: block;}
.principle_l button{ background: linear-gradient(to right,#6c97ee,#95b5f5); color: #fff; box-shadow: 0 0 3px #497be1;}
.principle_l li{ color: #7b7e88; position: relative;}
.principle_l li::after{ content: ''; position: absolute; left: 0; z-index: 9; background: #79a0f0; border-radius: 100%;}
.suit ul::after{ content:''; display:block; width: 0; height:0; clear:both; visibility:hidden;}
.suit li{ float: left; border: solid 1px #e1e1e1; position: relative; transition: all 0.2s;}
.suit i{ display: block; font-style: normal; background: #336de2;}
.suit p{ color: #7b7e88;}
.suit span{ position: absolute; z-index: 9; color: #e9edf4; font-family: pro-bold;}
.suit li:hover{ box-shadow: 0 0 30px #eee;}
.platform_word{ color: #787b85;}
.platform_it::after{ content:''; display:block; width: 0; height:0; clear:both; visibility:hidden;}
.platform_title h3{ color: #336de2;}
.platform_title p{ color: #e6e9f1; text-transform: uppercase;}
.platform_tab li{ position: relative; transition: background 0.1s,padding 0.5s;}
.platform_tab p{ font-weight: bold;}
.platform_tab span{ display: block; color: #787b85; text-transform: uppercase;}
.platform_tab i{ position: absolute; z-index: 9; background: #fff;}
.platform_tab .on{ background: #5287f2;}
.platform_tab .on p{ color: #fff;}
.platform_tab .on span{ color: #fff;}
.platform_tab .on i{ color: #fff;}
.platform_pic img{ width: 100%;}
.abut ul{ overflow: hidden;}
.abut li{ float: left; background: #fff; overflow: hidden;}
.abut .abut_pic{ float: left; text-align: center;}
.abut .abut_w{ float: left;}
.abut_w p{ color: #787b85;}
.abut li:hover{ background: #fcfcfc;}
.count_l{ float: left;}
.count_r{ float: right;}
.count_title{ border-bottom: solid 1px #e1e1e1;}
.count_title h2{ font-weight: normal;}
.count_title p{ color: #7b7e88;}
.count_ul ul{ overflow: hidden;}
.count_ul li{ float: left;}
.count_ul p{ color: #336de2; font-weight: bold;}
.count_ul span{ display: block; color: #7b7e88;}
.counter{ background: linear-gradient(135deg, #f2f4f8,#e6eaf2); border: solid 1px #e1e1e1;}
.counter button{ width: 100%; text-align: center; background: linear-gradient(to right,#336ce2,#2d4fd4); color: #fff; box-shadow: 0 3px 4px #4a61ba;}
.counter1 li{ overflow: hidden; position: relative; background: #fff;}
.counter1 span{ color: #336de2; float: left;}
.counter1 input{ background: #fff; text-align: right; float: right;}
.counter1 i{ font-style: normal; color: #336de2; position: absolute; top: 50%; transform: translateY(-50%); z-index: 9;}
.counter2{ overflow: hidden;}
.counter2 li{ float: left; overflow: hidden; border-bottom: solid 1px #e1e1e1;}
.counter2 p{ float: left;}
.counter2 span{ float: right; color: #787b85;}
.handbook ul{ display: flex; flex-wrap: wrap;}
.handbook li{ border: solid 1px #e1e1e1; background: linear-gradient(to bottom, #fff,#f2f6f9); transition: all 0.2s; overflow: hidden;}
.handbook_l{ float: left; text-align: center;}
.handbook_r{ float: right;}
.handbook_r p{ color: #787b85;}
.handbook_r span{ color: #336de2;}
.handbook li:hover{ box-shadow: 0 0 10px #dcdcdc;}
.handbook li:hover .handbook_r span{ font-weight: bold;}
.superiority ul{ overflow: hidden;}
.superiority li{ float: left; border: solid 1px #e1e1e1; overflow: hidden;}
.superiority01{ position: absolute; z-index: 99;}
.superiority02{ position: absolute; z-index: 99;}
.superiority02 img{ display: block;}
.superiority02 .super_icon02{ display: none;}
.superiority01 span{ display: block; color: #787b85;}
.superiority03{ width: 100%; height: 100%; position: absolute; top: 0; left: 0; z-index: 9; transition: all 0.2s; opacity: 0; visibility: hidden;}
.superiority03 img{ width: 100%; height: 100%; object-fit: cover;}
.superiority li:hover .super_icon01{ display: none;}
.superiority li:hover .super_icon02{ display: block;}
.superiority li:hover h1{ color: #fff;}
.superiority li:hover span{ color: #fff;}
.superiority li:hover .superiority03{ opacity: 1; visibility: visible;}
.banner_record{ width: 100%;}
.banner_record ul{ width: 100%; display: flex; justify-content: space-between; overflow: hidden; background: #fff; box-shadow: 0 10px 12px #f2f2f2;}
.banner_record li{ overflow: hidden;}
.banner_record section{ color: #336de2; font-family : pro-bold;}
.banner_record i{ font-style: normal; color: #336de2; vertical-align: top;}
.banner_record p{ color: #787b85;}
.about_l{ float: left;}
.about_p{ color: #4a4c54; text-align:justify}
.about_r{ float: right;}
.about_tu{ width: 100%; overflow: hidden;}
.about_tu img{ display: block; width: 100%; height: 100%; object-fit: cover;}
.settle ul{ overflow: hidden;}
.settle li{ overflow: hidden;}
.settle_pic{ float: left;}
.settle_w{ float: right;}
.settle_w p{ font-weight: bold;}
.settle_w span{ color: #787b85;}
.certificate_w{ color: #787b85;}
.certificate1{ float: left;}
.certificate2{ float: right;margin-top:8em}
#certificate_swiper .swiper-wrapper {
    -webkit-transition-timing-function: linear;
    -moz-transition-timing-function: linear;
    -ms-transition-timing-function: linear;
    -o-transition-timing-function: linear;
    transition-timing-function: linear;
    margin: 0 auto;
}
.certificate_pic{ border: solid 1px #e1e1e1;}
.certificate_pic img{ display: block; width: 100%;}
.cert_button{ position: absolute; left: 0; bottom: -1em; z-index: 9; overflow: hidden;}
.cert_button img{ display: block;}
.cert_button .cert_button_img02{ display: none;}
.cert_button_prev{ float: left; cursor: pointer;}
.cert_button_next{ float: right; cursor: pointer;}
.cert_button_prev:hover .cert_button_img01{ display: none;}
.cert_button_prev:hover .cert_button_img02{ display: block;}
.cert_button_next:hover .cert_button_img01{ display: none;}
.cert_button_next:hover .cert_button_img02{ display: block;}
.collaborate1{ float: left;}
.collaborate2{ float: right; background: #eceef4; overflow: hidden;}
.collaborate2-1{ float: right;  background:#fff; overflow: hidden;}
.collaborate1 span{ display: block; background: #336de2;}
.collaborate2a{ overflow: hidden;}
.collaborate2a input{ float: left; background: #fff;}
.collaborate3a{ overflow: hidden;}
.collaborate3a input{ float: left; background: #eceef4;}
.collaborate2b{ width: 100%;}
.collaborate2b textarea{ width: 100%; background: #fff; outline: 0; border: 0; resize: none;}
.collaborate3b{ width: 100%;}
.collaborate3b textarea{ width: 100%; background: #eceef4; outline: 0; border: 0; resize: none;}
.collaborate2c button{ background: linear-gradient(to right,#336de2,#2d4fd4); color: #fff;}
.collaborate3 span{ font-family: pro-bold; color: #336de2;}
.contact{ overflow: hidden; position: relative;}
.contact1{ float: left; overflow: hidden;}
.contact1 img{ width: 100%; height: 100%; object-fit: cover;} 
.contact2 p{/* font-weight: bold;*/}
.contact2 stronge{ font-weight: bold;}
.contact2 span{ display: block; color: #787b85;}
.content_l{ float: left;}
.content_r{ float: right;}
.information_title li{ color: #787b85;}
.information_title .infor_on{ color: #336de2;}
.information_list li{ overflow: hidden;}
.information_pic{ float: left; overflow: hidden;}
.information_pic img{ width: 100%; height: 100%; object-fit: cover; transition: all 0.3s;}
.information_w{ float: right; position: relative;}
.information_w h3{ overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.information_w p{ color: #787b85; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden;}
.information_w span{ color: #336de2; position: absolute; bottom: 0; left: 0; z-index: 9;}
.information_list li:hover .information_w h1{ color: #336de2;}
.information_title li:hover{ color: #336de2;}
.information_list li:hover .information_pic img{ transform: scale(1.08);}
.sidebar_title{ border-top: solid 1px #e1e1e1;}
.sidebar_title span{ display: inline-block; font-weight: bold;}
.hotspot li{ color: #787b85; position: relative; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}
.hotspot li::after{ content: ''; position: absolute; left: 0; top: 50%; transform: translateY(-50%); z-index: 9; background: #cacfda; border-radius: 50%;}
.hotspot li:hover{ color: #336de2;}
.case li{ overflow: hidden;}
.case_pic{ float: left; overflow: hidden;}
.case_pic img{ width: 100%; height: 100%; object-fit: cover; transition: all 0.5s;}
.case_word{ float: right; position: relative;}
.case_word p{ color: #787b85; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden;}
.case_word span{ color: #a2a2a2; position: absolute; left: 0; bottom: 0; z-index: 9;}
.case li:hover .case_pic img{ transform: scale(1.08);}
.case li:hover .case_word p{ color: #333;}
.service{ overflow: hidden;}
.service li{ float: left; border: solid 1px #bbd3e8;}
.service li:nth-child(1){ background: url(../../yibazhan/images/../../yibazhan/images/../img/ser_pic.jpg) center no-repeat; background-size: cover;}
.service li:nth-child(n+2){ background: linear-gradient(135deg,#eff5fc,#e5f1fc);}
.service li p{ color: #738599;}
.pagination{ text-align: center;}
.pagination ul{ display: inline-block;}
.pagination li{ display: inline-block; border: solid 1px #e1e1e1; text-align: center; color: #8c8d9e; transition: all 0.1s;}
.page_all{ display: inline-block; color: #8c8d9e;}
.pagination li:hover{ background: #336de2; color: #fff;}
.lead{ color: #787b85;}
.article_title{ border-bottom: solid 1px #e2e2e2;}
.article_title span{ display: block; color: #787b85;}
.article_content{ color: #787b85;}
.article_content p{ color: #787b85;}
.article_bor{ border-top: solid 1px #e1e1e1;}
.go_back{ display: inline-block; border: solid 1px #e1e1e1; transition: all 0.2s; cursor: pointer;}
.go_back span{ display: inline-block;}
.go_back:hover{ background: #336de2; }
.go_back:hover>span{ color: #fff;}
.page_link{ margin-bottom:2em;}
.page_link ul{ overflow: hidden;}
.page_link li{ border: solid 1px #cfd3e0; overflow: hidden; position: relative; transition: all 0.2s; cursor: pointer;}
.page_link li:hover{ background: #f5f6f9;}
.page_link .page_up{ float: left;}
.page_link .page_down{ float: right;}
.page_item{ background: #f5f6f9; text-align: center;}
.page_up .page_item{ position: absolute; height: 100%; top: 0; left: 0; z-index: 9; border-right: solid 1px #e1e1e1;}
.page_up .page_p{ float: right;}
.page_down .page_item{ position: absolute; height: 100%; top: 0; right: 0; z-index: 9; border-left: solid 1px #e1e1e1;}
.page_down .page_p{ float: left;}
.page_item img{ position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); z-index: 12;}
.page_p h6{ color: #336de2;}
.page_p p{ color: #787b85; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden;}
.page_p span{ color: #787b85;}
.use_01 span{ font-weight: bold; color: #f75555;}
.use_01 p{ display: inline-block;}
.use_02{ overflow: hidden;}
.use_02 input{ float: left; border: solid 1px #ccc; border-radius: 3px; outline: 0; box-sizing: border-box;}
.use_02 select{ float: left;  border: solid 1px #ccc; border-radius: 3px; outline: 0; outline: 0;}
.use_02 textarea{ width: 100%; border: solid 1px #ccc; outline: 0; resize: none;}
.choice{ overflow: hidden;}
.choice input{ float: left;}
.choice span{ float: left;}
.use_form_btn button{ text-align: center; background: linear-gradient(to right,#336be1,#2d4fd4); color: #fff; cursor: pointer; font-weight: bold;}
.use_form_btn button:hover{ background: #2d4fd4;}
table.plzc{ border:1px #ccc solid; width:100%; background:#ced1da;}
.plzc td{ padding:1em 2em; box-sizing:border-box; background:#fff;}
.plzc h1{ font-size:1.125em;color:#336de2}
table.plzc1{ border:1px #ccc solid; width:100%; background:#ced1da;}
.plzc1 td{ padding:1em 2em; box-sizing:border-box; background:#fff; width:20%;}
.plzc1 tr:nth-child(2n+1)>td{background:#f6f6f6}
.plzc1 th{ padding:1em 2em; background:#ecf0f8;}
.plzc1 th h1{color:#336de2}
.plzc1 td h1{ color:#336de2; font-weight:normal;}
.intro-right{ position:absolute; width:40%; top:0; right:0; text-align:right; font-size:1em;}
.intro-right span{color:#336de2}
.brief_general img.gtgsqy-pc{display:block;width:100%; height:auto;}
.brief_general img.gtgsqy-m{display:none;}
.brief_general .text{ width:100%; margin-top:1em; text-align:center; font-size:1.25em;}
.brief_general .text span{font-size:1.1em; color:#336de2;margin:0 0.2em;}
/*尾部*/
.ilinks{margin:auto; width:100%;}
.links{margin:auto;  padding-top:20px; padding-bottom:10px;display:flex;}
.linkss{background:#484848; width:80px; line-height:25px; height:25px;text-align:center;font-size:16px;color:#999999;margin:0 20px 0 0;}
.links ul{flex:1;overflow:hidden;font-size:14px;line-height:25px;display:flex;flex-wrap:wrap}
.links ul a{color:#999fb1;padding-right: 10px;}

/**********联系我们修***********/
.contact_map_head{display: flex;justify-content: space-between;}
.contact_map_head .li{width: 24%;position: relative;height: 16em;overflow: hidden;}
.contact_map_head .li img{display:block;width:100%;height:100%}
.contact_map_head .li .dq{position: absolute;top:10px;left:10px;font-size:20px;color:#fff;z-index: 2;}
.contact_map_head .li.active::after{content: "";display: block;position: absolute;top: 0;left:0;width:100%;height:100%;background: rgb(8 79 129 / 50%);z-index: 1;}
.contact_detail_list{margin:20px auto;display:flex;align-items: center;justify-content: center;}
.contact_detail_list .text{width:45%;padding-right:5%;}
.contact_detail_list .text h2{font-size:24px;font-weight: bold;margin-bottom: 2em;}
.contact_detail_list .text p{font-size:16px;margin-bottom: 2em;}
.contact_detail_list .map_img{width: 55%;height:400px}