function isMobile() {
    var userAgentInfo = navigator.userAgent;
    var mobileAgents = [ "Android", "iPhone", "SymbianOS", "Windows Phone", "iPad","iPod"];
    var mobile_flag = false;
    for (var v = 0; v < mobileAgents.length; v++) {
        if (userAgentInfo.indexOf(mobileAgents[v]) > 0) {
            mobile_flag = true;
            break;
        }
    }
     var screen_width = window.screen.width;
     var screen_height = window.screen.height;    
     if(screen_width < 500 && screen_height < 800){
         mobile_flag = true;
     }
     return mobile_flag;
}
function checkjq(){
	if(typeof(jQuery)=="undefined"){
		alert("您没有引用Jquery");
	}
}
function show(e){
	$("#"+e).show()
}
function hide(e){
	$("#"+e).hide()
}
function showmobcon(){
	$("#zx_mobile_contents").show()
}
function showmobewm(){
	$("#zx_mobile_content1").show()
}
 function zixunchuang(){
    var iWidth=800; 
	var iHeight=600;
	var iTop = (window.screen.availHeight-30-iHeight)/2;
	var iLeft = (window.screen.availWidth-10-iWidth)/2;
	window.open(baidu,"","height="+iHeight+", width="+iWidth+", top="+iTop+", left="+iLeft); 
	hide("feeyr_zixun")
};
function mobileopen(){
  $("#nb_icon_wrap").click()
}
function name(params) {

let newpar = parseFloat(params);

let reg = /^[0-9]+.?[0-9]*$/;

if(reg.test(newpar)){

let newNum = newpar.toFixed(3);

return newNum;
}
}
function openqq(){
	var u = navigator.userAgent;
            var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
            if(isiOS){
                if(u.toLowerCase().match(/MicroMessenger/i) == "micromessenger"){
                    window.open('http://wpa.qq.com/msgrd?v=3&uin='+qq+'&site=qq&menu=yes');
                }else{
                    window.open('mqqwpa://im/chat?chat_type=wpa&uin='+qq+'&version=1&src_type=web&web_src=oicqzone.com')         
                }
            }else{
                if(u.toLowerCase().match(/MicroMessenger/i) == "micromessenger"){
                    window.open('http://wpa.qq.com/msgrd?v=3&uin='+qq+'&site=qq&menu=yes');
                }else{
                    window.open('mqqwpa://im/chat?chat_type=wpa&uin='+qq+'&version=1&src_type=web&web_src=oicqzone.com')
				}
            }
}
checkjq()
var script = document.getElementsByTagName("script")
eval(script[script.length-1].innerHTML)
$(function(){
    if(isMobile()){
    var fysz = $('html').css('font-size')
    var ffy = fysz.split('px')
	var ffye = ffy[0].split('.')
	if(ffye[1]){
		var fysznow = Number(name(ffye[0]))
	}else{
		var fysznow = Number(ffye[0])
	}
    fysznow =24
    
      var fycss_1 = name(10/fysznow*7.5)
	  var fycss_2 = name(fycss_1/5)
	  var fycss_3 = name(fycss_1/3)
	  var fycss_4 = name(fycss_1/12)
	  var fycss_5 = 3.75
	  var fycss_6 = name(fycss_1/0.3)
	  var fycss_7 = name(fycss_1/4)
	  var fycss_8 = name(fycss_1/2)
	  var fycss_9 = name(fycss_1/12)
	  var fycss_10 = name(fycss_1/6)
	  var fycss_11 = name(fycss_1/2.5)
	  var fycss_12 = name(fycss_1/1.5)
	  var fycss_13 = name(fycss_1/3.33)
	  var fycss_14 = name(fycss_1/3)
	  var fycss_15 = name(fycss_1/6)
	  var fycss_16 = 2
	  var fycss_17 = name(fycss_1/9.3)
	  var fycss_18 = 14.5
      var divhe = 10/fysznow*6
	//  var fytjstyle =".feeyr_footer{width: 100%;height:"+fycss_1+"rem;background:rgba(218,34,34,1);position: fixed;bottom: 0;text-align:center; font-family:Microsoft YaHei;z-index: 50000; padding:0; margin:0;} .feeyr_footer a{ width:24.5%; float:left;font-size:"+fycss_2+"rem; text-align:center; color:#fff; line-height:"+fycss_3+"rem; display: inline-block;  margin:0;  padding:"+fycss_4+"rem 0; border-right:1px #fff solid;} .feeyr_footer a:nth-child(4){border:none;} .feeyr_footer img{width:"+fycss_5+"rem; height:"+fycss_5+"rem;; line-height:"+fycss_5+"rem;; vertical-align:middle;  margin:0 auto; display:block;}	 .feeyr_contents{ width:100%; height: 100%; position: fixed; z-index:1000; background:rgba(0,0,0,0.8); top: 0; left:0; text-align: center;} .feeyr_contents .zixun_top{width:75%; margin:3% auto 0 auto; height:auto;} .feeyr_contents .zixun_top img{ width:100%; height:auto;}.feeyr_contents .zixun_feeyr{ width:75%;height:"+fycss_6+"rem; margin:0 auto; padding:"+fycss_16+"rem 0 "+fycss_16+"rem 0; border-radius:0 0 "+fycss_4+"rem "+fycss_4+"rem; background:#fff; margin-top:-"+fycss_17+"rem; 	} .feeyr_contents .zixun_feeyr input{  width:90%; line-height:3rem;border: none; margin:0 auto "+fycss_7+"rem auto;  height:"+fycss_8+"rem; padding:"+fycss_9+"rem ; text-indent: "+fycss_10+"rem; font-size:"+fycss_2+"rem; border:1px solid #999; text-align: left;  background:#fff; }	.feeyr_contents .zixun_feeyr input:nth-child(4){ line-height:3rem; height:3rem;  box-sizing: content-box; padding:"+fycss_4+"rem 0; font-size: "+fycss_7+"rem;display:block;  text-align: center; background:#da2222; color:#fff; border:1px solid #da2222;  -webkit-appearance: none;}.close_feeyr{ width:"+fycss_12+"rem; height:"+fycss_12+"rem; border:2px #fff solid;  line-height: "+fycss_12+"rem; text-align: center; font-size: "+fycss_13+"rem;  border-radius:55%; position: absolute; left:50%; margin-left: -"+fycss_14+"rem; margin-top:"+fycss_15+"rem;}.close_feeyr a{ color:#fff;font-size:"+fycss_16+"rem;}";
      
      
    	$("#zx_r").hide()
      	
      if(bcolor == 1){
        	
        $("#zx_mobile_bottom").addClass("feeyr_footer")
        $("#zx_mobile_bottom").css('height',divhe+'rem')
          $("#zx_mobile_contents").addClass("feeyr_contents")
          $("#zx_mobile_content1").addClass("feeyr_contents")
        }else if(bcolor == 2){
        	$("#zx_mobile_bottom").addClass("feeyr_footer")
          	$("#zx_mobile_contents").addClass("feeyr_contents")
          	 $("#zx_mobile_content1").addClass("feeyr_contents")
        }else if(bcolor == 3){
        	$("#zx_mobile_bottom").addClass("feeyr_footer2")
           $("#zx_mobile_contents").addClass("feeyr_contents2")
            $("#zx_mobile_content1").addClass("feeyr_contents")
        }
      //	$("#zx_mobile_bottom").addClass("feeyr_footer1")
      	
      	$("#zx_mobile_contents").hide()
      	var btlink = "<a href='tel:"+telmobile+"' style='width:24.5%; float:left;font-size:"+fycss_2+"rem; text-align:center; color:#fff; line-height:"+fycss_3+"rem; display: inline-block;  margin:0;  padding:"+fycss_4+"rem 0; border-right:1px #fff solid;'><img src='../img/tel.png' style='width:"+fycss_3+"rem; height:"+fycss_3+"rem;; line-height:"+fycss_3+"rem;; vertical-align:middle;  margin:0 auto; display:block;'  alt=''/>"+left+"</a><a onclick='showmobewm()' href='javascript:void(0)'  style='width:24.5%; float:left;font-size:"+fycss_2+"rem; text-align:center; color:#fff; line-height:"+fycss_3+"rem; display: inline-block;  margin:0;  padding:"+fycss_4+"rem 0; border-right:1px #fff solid;'><img src='../img/wechat.png' style='width:"+fycss_3+"rem; height:"+fycss_3+"rem;; line-height:"+fycss_3+"rem;; vertical-align:middle;  margin:0 auto; display:block;'  alt=''/>微信客服</a><a href='https://p.qiao.baidu.com/cps/chat?siteId=17870182&userId=34482745&siteToken=0b96c1021d11d6fcfbbf618cf01d6a04' onclick='mobileopen()' style='width:24.5%; float:left;font-size:"+fycss_2+"rem; text-align:center; color:#fff; line-height:"+fycss_3+"rem; display: inline-block;  margin:0;  padding:"+fycss_9+"rem 0; border-right:1px #fff solid;'><img src='../img/zx.png' style='width:"+fycss_3+"rem; height:"+fycss_3+"rem;; line-height:"+fycss_3+"rem;; vertical-align:middle;  margin:0 auto; display:block;'  alt=''/>"+middle+"</a><a onclick='showmobcon()' href='javascript:void(0)' style='width:24.5%; float:left;font-size:"+fycss_2+"rem; text-align:center; color:#fff; line-height:"+fycss_3+"rem; display: inline-block;  margin:0;  padding:"+fycss_9+"rem 0;'><img src='../img/bd.png' style='width:"+fycss_3+"rem; height:"+fycss_3+"rem;; line-height:"+fycss_3+"rem;; vertical-align:middle;  margin:0 auto; display:block;'  alt=''/>"+right+"</a>";
      	$("#zx_mobile_bottom").html(btlink)
      	
      	var mobcon = "<div style='width:75%; margin:15vh auto 0 auto; height:auto;'><img src='../img/zixun_top_feeyr.png' style='width:100%; height:auto;'></div><div style='width:75%;height:"+fycss_18+"rem; margin:0 auto; padding:"+fycss_8+"rem 0 "+fycss_16+"rem 0; border-radius:0 0 "+fycss_9+"rem "+fycss_9+"rem; background:#fff; margin-top:-"+fycss_17+"rem;'><input type='text' id='feeyrbaiduname' placeholder='*输入您的姓名' style='width:90%; line-height:"+fycss_11+"rem;border: none; margin:0 auto "+fycss_7+"rem auto;  height:"+fycss_8+"rem; padding:"+fycss_9+"rem ; text-indent: "+fycss_10+"rem; font-size:"+fycss_2+"rem; border:1px solid #999; text-align: left;  background:#fff; '><input type='number' id='feeyrbaidutelephone' placeholder='*输入您的联系方式'  style='width:90%; line-height:"+fycss_11+"rem;border: none; margin:0 auto "+fycss_7+"rem auto;  height:"+fycss_8+"rem; padding:"+fycss_9+"rem ; text-indent: "+fycss_10+"rem; font-size:"+fycss_2+"rem; border:1px solid #999; text-align: left;  background:#fff; '><input type='text' id='gongsiming' placeholder='*输入您的公司名称'  style='width:90%; line-height:"+fycss_11+"rem;border: none; margin:0 auto "+fycss_7+"rem auto;  height:"+fycss_8+"rem; padding:"+fycss_9+"rem ; text-indent: "+fycss_10+"rem; font-size:"+fycss_2+"rem; border:1px solid #999; text-align: left;  background:#fff; '><input type='text' id='feeyrbaiduneirong' placeholder='输入您的需求(选填)'  style='width:90%; line-height:"+fycss_11+"rem;border: none; margin:0 auto "+fycss_7+"rem auto;  height:"+fycss_8+"rem; padding:"+fycss_9+"rem ; text-indent: "+fycss_10+"rem; font-size:"+fycss_2+"rem; border:1px solid #999; text-align: left;  background:#fff; '><input type='button' id='subme' value='提交需求' style='line-height:"+fycss_11+"rem; height:"+fycss_11+"rem;  box-sizing: content-box; margin:"+fycss_7+"rem  auto 0 auto; width:90%; padding:"+fycss_9+"rem 0; font-size: "+fycss_7+"rem;display:block;  text-align: center; background:#094e9c; color:#fff; border:1px solid #094e9c;  -webkit-appearance: none;'></div><div style='width:"+fycss_8+"rem; height:"+fycss_8+"rem;  line-height: "+fycss_8+"rem; text-align: center; font-size: "+fycss_7+"rem; position: absolute; left:80%;top:13vh;margin-left: -"+fycss_7+"rem; margin-top:"+fycss_8+"rem;'><a style='color:#094e9c;font-size:"+fycss_7+"rem;' href='javascript:void(0)' onclick=hide('zx_mobile_contents')>X</a></div>";
		$("#zx_mobile_contents").html(mobcon)
		
		
		$("#zx_mobile_content1").hide()	
		var mobewm = "<div style='width:75%; margin:25vh auto 0 auto; height:auto; background:#fff; height:32vh;  position:relative; border-radius:1rem;'><div style='padding-top:2rem; '>打开您的微信，添加好友</div><p id='text' style='display: none'>13910101804</p><div style='margin:5%; height:2rem; background:#ededed; line-height:2rem; border-radius:1rem; font-size:0.7rem; font-weight:bold; position:relative;' id='copy' ><span style=' position:absolute;left:1rem;top:0;'>微信号</span><textarea readonly id='input' style='width:90%; text-align:right; border:0; outline: 0;resize: none;font-size: 0.7rem;width: 50%;line-height:2rem; height:2rem; background: transparent;'>13910101804</textarea></div><button style='margin:0 auto; border-radius:0.3rem; width:50%; padding:0.5rem 1rem; background:#05b114; color:#fff;' class='copyBtn_1'>复制微信号</button><a style='color:#094e9c;font-size:"+fycss_7+"rem; position:absolute; top:1rem; right:1rem;' href='javascript:void(0)' onclick=hide('zx_mobile_content1')>X</a></div>";
		$("#zx_mobile_content1").html(mobewm)
	  
	   function tipword(str){
   
     $(".tipword").html(str)
    	
    $(".tipword").addClass("tipword01")
    	
     setTimeout(function(){
    		
           $(".tipword").removeClass("tipword01")
    	
        },3000)
      
  }
   
	       $(".copyBtn_1").click(function(){
        var ua = window.navigator.userAgent.toLowerCase();
        // if(ua.match(/MicroMessenger/i) == 'micromessenger'){
        var text = document.getElementById("text").innerText;
        var input = document.getElementById("input");
        input.value = text; // 修改文本框的内容
        input.select(); // 选中文本
        document.execCommand("copy"); // 执行浏览器复制命令
            setTimeout(function(){
                window.location.href="weixin://"
               tipword('复制成功'); 
            },300);
            
        // }else{
            // window.location.href = "weixin://";
        // };
    });
	  
	  
    }else{
		$("#zx_mobile_bottom").hide()
		$("#zx_mobile_contents").hide()
		$("#zx_mobile_content1").hide()
		var baiduone = one;
		var htmlcontent = '<dl><dt></dt><dd class="consult"><span href="javascript:void(0)" style="color:#fff;" >电话咨询</span><div class="floating_left floating_tel"><p class="tel_1">全国热线</p><b class="tel_2">400-0880-104</b></div></dd><dd class="words"><span  onclick="zixunchuang()">在线咨询</span></dd><dd class="qrcord"><span>微信咨询</span></a><div class="floating_left floating_ewm"><i><img src="'+four+'" width="145" height="145" alt=""/></i></div></dd><dd class="return"><a onclick=show("feeyr_zixun")> <span>注册体验</span></a></dd></dl></div><div id="feeyr_zixun"><form id="zform" class="clearfix" action="/form/5/" enctype="multipart/form-data" method="post"> <div class="close" id="close_c" onclick=hide("feeyr_zixun")><span style="color:#fff;" >X</span></div><div class="top_bt">'+baiduone+'<p>'+two+'</p></div><div class="left_info"><ul class="info"><li>您可以直接电话快速沟通</li><li>'+three+'</li><li><div class="pic"><img src="'+four+'" width="145" height="145" alt=""/></div><div class="text">扫一扫，微信咨询</div></li><li><a onclick="zixunchuang()" >在线咨询</a><a href="https://wpa.qq.com/msgrd?v=3&uin='+qq+'&site=qq&menu=yes" >QQ咨询</a></li></ul></div><div class="right_feedback"><ul class="feedback"><li><font color="black"  style="font-size:1em;">免费试用系统，体验灵工业务全过程<br><span style="font-size:0.7em;color:#999;">我们将在一个工作日内与您联系<span></font></li> <li><i>*</i><input type="text" name="contacts" id="feeyrbaiduname"placeholder="您的姓名"/></li><li><i>*</i><input type="text" name="tel" id="feeyrbaidutelephone" placeholder="联系电话"/></li><li><i>*</i><input type="text" name="cname" id="gongsiming" placeholder="您的公司名称"/></li><li><input type="text" id="feeyrbaiduneirong" name="content" placeholder="请简单描述您的需求(选填)"/></li><li><input type="button" id="subme" value="提交申请" class="botton"/></li></ul></div></form>'
		$("#zx_r").html(htmlcontent)
		var num=150000;
		let todayopen = localStorage.getItem("istodayopen")
        let bddate=new Date();
        let bdyear=bddate.getFullYear();
        let bdmonth=bddate.getMonth()+1;
        let bdday=bddate.getDate();
        let todaylg = bdyear+'-'+bdmonth+'-'+bdday
		if(todayopen && todayopen==todaylg){
		    console.log(11)
		}else{
		var timer=setInterval(function(){
			num--;
			if(num==0){
				$("#feeyr_zixun").slideDown()
				localStorage.setItem("istodayopen",todaylg)
			}
		},1000)
    }
    }
	$("#subme").click(function(){
      $(this).val('邮件已发送...')
    	var name = $("#feeyrbaiduname").val()
        var tel = $("#feeyrbaidutelephone").val()
        var con = $("#gongsiming").val()
        if(name=='' || tel=='' || con==''){
        	alert('信息不完整')
          $(this).val('补充信息再次发送')
          	return false
        }
		
		$('#zform').submit();
      	
        var txt = '电话为'+tel+'的客户'+name+'公司名'+con
        var surl = "http://kaoshi.feeyr.com/feeyrbaidu/index.php?txt="+txt+"&email="+mail+"&company="+comname
      $.ajax({url:surl,async:false})
		
		
      	alert('已发送邮件，我们会及时联系您！')
      	hide("feeyr_zixun")
      	hide("zx_mobile_contents")
      	hide("zx_mobile_content1")
    })
     $('#gotoback').click(function(){
		$("html,body").animate({scrollTop:0},"fast");
	});
})