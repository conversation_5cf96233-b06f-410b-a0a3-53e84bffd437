$(function(){
	hScroll();
	function hScroll() {
        var i = $(window).scrollTop();
        gradual();
        $(window).scroll(function() {
            i = $(this).scrollTop();
            gradual();
        });
        
        function gradual(){
    	    if (i > 50) {
    	        $(".header").addClass("headerAct");
        		$(".header").addClass("header_bg");
        		$(".header").removeClass("header_bor");
        		$(".header_logo span").addClass("header_hue");
        		$(".header_logo span").removeClass("login_line");
        		$(".login").removeClass("login_line");
        		$(".header_word01 a").addClass("header_hue");
        		$(".logo_tu01,.tell_tu01,.earth_tu01,.search_icon01,.code_tu01").hide();
        		$(".logo_tu02,.tell_tu02,.earth_tu02,.search_icon02,.code_tu02").show();
        		$(".earth_tu span").addClass("header_hue");
        		$(".login span").addClass("header_hue");
        		$(".login i").addClass("header_hue");
        		$(".searcher").removeClass("searcher_line");
        		$(".header_menu .bill_tu01").hide();
        		$(".header_menu .bill_tu02").show();
    	    }else{
    	        $(".header").removeClass("headerAct");
        		$(".header").removeClass("header_bg");
        		$(".header").addClass("header_bor");
        		$(".header_logo span").removeClass("header_hue");
        		$(".header_logo span").addClass("login_line");
        		$(".login").addClass("login_line");
        		$(".header_word01 a").removeClass("header_hue");
        		$(".logo_tu01,.tell_tu01,.earth_tu01,.search_icon01,.code_tu01").show();
        		$(".logo_tu02,.tell_tu02,.earth_tu02,.search_icon02,.code_tu02").hide();
        		$(".earth_tu span").removeClass("header_hue");
        		$(".login span").removeClass("header_hue");
        		$(".login i").removeClass("header_hue");
        		$(".searcher").addClass("searcher_line");
        		$(".header_menu .bill_tu01").show();
        		$(".header_menu .bill_tu02").hide();
    	    }
	    }
	}
	
})