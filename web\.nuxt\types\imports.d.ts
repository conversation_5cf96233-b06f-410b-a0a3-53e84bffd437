// Generated by auto imports
export {}
declare global {
  const ElIconAddLocation: typeof import('@element-plus/icons-vue')['AddLocation']
  const ElIconAim: typeof import('@element-plus/icons-vue')['Aim']
  const ElIconAlarmClock: typeof import('@element-plus/icons-vue')['AlarmClock']
  const ElIconApple: typeof import('@element-plus/icons-vue')['Apple']
  const ElIconArrowDown: typeof import('@element-plus/icons-vue')['ArrowDown']
  const ElIconArrowDownBold: typeof import('@element-plus/icons-vue')['ArrowDownBold']
  const ElIconArrowLeft: typeof import('@element-plus/icons-vue')['ArrowLeft']
  const ElIconArrowLeftBold: typeof import('@element-plus/icons-vue')['ArrowLeftBold']
  const ElIconArrowRight: typeof import('@element-plus/icons-vue')['ArrowRight']
  const ElIconArrowRightBold: typeof import('@element-plus/icons-vue')['ArrowRightBold']
  const ElIconArrowUp: typeof import('@element-plus/icons-vue')['ArrowUp']
  const ElIconArrowUpBold: typeof import('@element-plus/icons-vue')['ArrowUpBold']
  const ElIconAvatar: typeof import('@element-plus/icons-vue')['Avatar']
  const ElIconBack: typeof import('@element-plus/icons-vue')['Back']
  const ElIconBaseball: typeof import('@element-plus/icons-vue')['Baseball']
  const ElIconBasketball: typeof import('@element-plus/icons-vue')['Basketball']
  const ElIconBell: typeof import('@element-plus/icons-vue')['Bell']
  const ElIconBellFilled: typeof import('@element-plus/icons-vue')['BellFilled']
  const ElIconBicycle: typeof import('@element-plus/icons-vue')['Bicycle']
  const ElIconBottom: typeof import('@element-plus/icons-vue')['Bottom']
  const ElIconBottomLeft: typeof import('@element-plus/icons-vue')['BottomLeft']
  const ElIconBottomRight: typeof import('@element-plus/icons-vue')['BottomRight']
  const ElIconBowl: typeof import('@element-plus/icons-vue')['Bowl']
  const ElIconBox: typeof import('@element-plus/icons-vue')['Box']
  const ElIconBriefcase: typeof import('@element-plus/icons-vue')['Briefcase']
  const ElIconBrush: typeof import('@element-plus/icons-vue')['Brush']
  const ElIconBrushFilled: typeof import('@element-plus/icons-vue')['BrushFilled']
  const ElIconBurger: typeof import('@element-plus/icons-vue')['Burger']
  const ElIconCalendar: typeof import('@element-plus/icons-vue')['Calendar']
  const ElIconCamera: typeof import('@element-plus/icons-vue')['Camera']
  const ElIconCameraFilled: typeof import('@element-plus/icons-vue')['CameraFilled']
  const ElIconCaretBottom: typeof import('@element-plus/icons-vue')['CaretBottom']
  const ElIconCaretLeft: typeof import('@element-plus/icons-vue')['CaretLeft']
  const ElIconCaretRight: typeof import('@element-plus/icons-vue')['CaretRight']
  const ElIconCaretTop: typeof import('@element-plus/icons-vue')['CaretTop']
  const ElIconCellphone: typeof import('@element-plus/icons-vue')['Cellphone']
  const ElIconChatDotRound: typeof import('@element-plus/icons-vue')['ChatDotRound']
  const ElIconChatDotSquare: typeof import('@element-plus/icons-vue')['ChatDotSquare']
  const ElIconChatLineRound: typeof import('@element-plus/icons-vue')['ChatLineRound']
  const ElIconChatLineSquare: typeof import('@element-plus/icons-vue')['ChatLineSquare']
  const ElIconChatRound: typeof import('@element-plus/icons-vue')['ChatRound']
  const ElIconChatSquare: typeof import('@element-plus/icons-vue')['ChatSquare']
  const ElIconCheck: typeof import('@element-plus/icons-vue')['Check']
  const ElIconChecked: typeof import('@element-plus/icons-vue')['Checked']
  const ElIconCherry: typeof import('@element-plus/icons-vue')['Cherry']
  const ElIconChicken: typeof import('@element-plus/icons-vue')['Chicken']
  const ElIconChromeFilled: typeof import('@element-plus/icons-vue')['ChromeFilled']
  const ElIconCircleCheck: typeof import('@element-plus/icons-vue')['CircleCheck']
  const ElIconCircleCheckFilled: typeof import('@element-plus/icons-vue')['CircleCheckFilled']
  const ElIconCircleClose: typeof import('@element-plus/icons-vue')['CircleClose']
  const ElIconCircleCloseFilled: typeof import('@element-plus/icons-vue')['CircleCloseFilled']
  const ElIconCirclePlus: typeof import('@element-plus/icons-vue')['CirclePlus']
  const ElIconCirclePlusFilled: typeof import('@element-plus/icons-vue')['CirclePlusFilled']
  const ElIconClock: typeof import('@element-plus/icons-vue')['Clock']
  const ElIconClose: typeof import('@element-plus/icons-vue')['Close']
  const ElIconCloseBold: typeof import('@element-plus/icons-vue')['CloseBold']
  const ElIconCloudy: typeof import('@element-plus/icons-vue')['Cloudy']
  const ElIconCoffee: typeof import('@element-plus/icons-vue')['Coffee']
  const ElIconCoffeeCup: typeof import('@element-plus/icons-vue')['CoffeeCup']
  const ElIconCoin: typeof import('@element-plus/icons-vue')['Coin']
  const ElIconColdDrink: typeof import('@element-plus/icons-vue')['ColdDrink']
  const ElIconCollection: typeof import('@element-plus/icons-vue')['Collection']
  const ElIconCollectionTag: typeof import('@element-plus/icons-vue')['CollectionTag']
  const ElIconComment: typeof import('@element-plus/icons-vue')['Comment']
  const ElIconCompass: typeof import('@element-plus/icons-vue')['Compass']
  const ElIconConnection: typeof import('@element-plus/icons-vue')['Connection']
  const ElIconCoordinate: typeof import('@element-plus/icons-vue')['Coordinate']
  const ElIconCopyDocument: typeof import('@element-plus/icons-vue')['CopyDocument']
  const ElIconCpu: typeof import('@element-plus/icons-vue')['Cpu']
  const ElIconCreditCard: typeof import('@element-plus/icons-vue')['CreditCard']
  const ElIconCrop: typeof import('@element-plus/icons-vue')['Crop']
  const ElIconDArrowLeft: typeof import('@element-plus/icons-vue')['DArrowLeft']
  const ElIconDArrowRight: typeof import('@element-plus/icons-vue')['DArrowRight']
  const ElIconDCaret: typeof import('@element-plus/icons-vue')['DCaret']
  const ElIconDataAnalysis: typeof import('@element-plus/icons-vue')['DataAnalysis']
  const ElIconDataBoard: typeof import('@element-plus/icons-vue')['DataBoard']
  const ElIconDataLine: typeof import('@element-plus/icons-vue')['DataLine']
  const ElIconDelete: typeof import('@element-plus/icons-vue')['Delete']
  const ElIconDeleteFilled: typeof import('@element-plus/icons-vue')['DeleteFilled']
  const ElIconDeleteLocation: typeof import('@element-plus/icons-vue')['DeleteLocation']
  const ElIconDessert: typeof import('@element-plus/icons-vue')['Dessert']
  const ElIconDiscount: typeof import('@element-plus/icons-vue')['Discount']
  const ElIconDish: typeof import('@element-plus/icons-vue')['Dish']
  const ElIconDishDot: typeof import('@element-plus/icons-vue')['DishDot']
  const ElIconDocument: typeof import('@element-plus/icons-vue')['Document']
  const ElIconDocumentAdd: typeof import('@element-plus/icons-vue')['DocumentAdd']
  const ElIconDocumentChecked: typeof import('@element-plus/icons-vue')['DocumentChecked']
  const ElIconDocumentCopy: typeof import('@element-plus/icons-vue')['DocumentCopy']
  const ElIconDocumentDelete: typeof import('@element-plus/icons-vue')['DocumentDelete']
  const ElIconDocumentRemove: typeof import('@element-plus/icons-vue')['DocumentRemove']
  const ElIconDownload: typeof import('@element-plus/icons-vue')['Download']
  const ElIconDrizzling: typeof import('@element-plus/icons-vue')['Drizzling']
  const ElIconEdit: typeof import('@element-plus/icons-vue')['Edit']
  const ElIconEditPen: typeof import('@element-plus/icons-vue')['EditPen']
  const ElIconEleme: typeof import('@element-plus/icons-vue')['Eleme']
  const ElIconElemeFilled: typeof import('@element-plus/icons-vue')['ElemeFilled']
  const ElIconElementPlus: typeof import('@element-plus/icons-vue')['ElementPlus']
  const ElIconExpand: typeof import('@element-plus/icons-vue')['Expand']
  const ElIconFailed: typeof import('@element-plus/icons-vue')['Failed']
  const ElIconFemale: typeof import('@element-plus/icons-vue')['Female']
  const ElIconFiles: typeof import('@element-plus/icons-vue')['Files']
  const ElIconFilm: typeof import('@element-plus/icons-vue')['Film']
  const ElIconFilter: typeof import('@element-plus/icons-vue')['Filter']
  const ElIconFinished: typeof import('@element-plus/icons-vue')['Finished']
  const ElIconFirstAidKit: typeof import('@element-plus/icons-vue')['FirstAidKit']
  const ElIconFlag: typeof import('@element-plus/icons-vue')['Flag']
  const ElIconFold: typeof import('@element-plus/icons-vue')['Fold']
  const ElIconFolder: typeof import('@element-plus/icons-vue')['Folder']
  const ElIconFolderAdd: typeof import('@element-plus/icons-vue')['FolderAdd']
  const ElIconFolderChecked: typeof import('@element-plus/icons-vue')['FolderChecked']
  const ElIconFolderDelete: typeof import('@element-plus/icons-vue')['FolderDelete']
  const ElIconFolderOpened: typeof import('@element-plus/icons-vue')['FolderOpened']
  const ElIconFolderRemove: typeof import('@element-plus/icons-vue')['FolderRemove']
  const ElIconFood: typeof import('@element-plus/icons-vue')['Food']
  const ElIconFootball: typeof import('@element-plus/icons-vue')['Football']
  const ElIconForkSpoon: typeof import('@element-plus/icons-vue')['ForkSpoon']
  const ElIconFries: typeof import('@element-plus/icons-vue')['Fries']
  const ElIconFullScreen: typeof import('@element-plus/icons-vue')['FullScreen']
  const ElIconGoblet: typeof import('@element-plus/icons-vue')['Goblet']
  const ElIconGobletFull: typeof import('@element-plus/icons-vue')['GobletFull']
  const ElIconGobletSquare: typeof import('@element-plus/icons-vue')['GobletSquare']
  const ElIconGobletSquareFull: typeof import('@element-plus/icons-vue')['GobletSquareFull']
  const ElIconGoldMedal: typeof import('@element-plus/icons-vue')['GoldMedal']
  const ElIconGoods: typeof import('@element-plus/icons-vue')['Goods']
  const ElIconGoodsFilled: typeof import('@element-plus/icons-vue')['GoodsFilled']
  const ElIconGrape: typeof import('@element-plus/icons-vue')['Grape']
  const ElIconGrid: typeof import('@element-plus/icons-vue')['Grid']
  const ElIconGuide: typeof import('@element-plus/icons-vue')['Guide']
  const ElIconHandbag: typeof import('@element-plus/icons-vue')['Handbag']
  const ElIconHeadset: typeof import('@element-plus/icons-vue')['Headset']
  const ElIconHelp: typeof import('@element-plus/icons-vue')['Help']
  const ElIconHelpFilled: typeof import('@element-plus/icons-vue')['HelpFilled']
  const ElIconHide: typeof import('@element-plus/icons-vue')['Hide']
  const ElIconHistogram: typeof import('@element-plus/icons-vue')['Histogram']
  const ElIconHomeFilled: typeof import('@element-plus/icons-vue')['HomeFilled']
  const ElIconHotWater: typeof import('@element-plus/icons-vue')['HotWater']
  const ElIconHouse: typeof import('@element-plus/icons-vue')['House']
  const ElIconIceCream: typeof import('@element-plus/icons-vue')['IceCream']
  const ElIconIceCreamRound: typeof import('@element-plus/icons-vue')['IceCreamRound']
  const ElIconIceCreamSquare: typeof import('@element-plus/icons-vue')['IceCreamSquare']
  const ElIconIceDrink: typeof import('@element-plus/icons-vue')['IceDrink']
  const ElIconIceTea: typeof import('@element-plus/icons-vue')['IceTea']
  const ElIconInfoFilled: typeof import('@element-plus/icons-vue')['InfoFilled']
  const ElIconIphone: typeof import('@element-plus/icons-vue')['Iphone']
  const ElIconKey: typeof import('@element-plus/icons-vue')['Key']
  const ElIconKnifeFork: typeof import('@element-plus/icons-vue')['KnifeFork']
  const ElIconLightning: typeof import('@element-plus/icons-vue')['Lightning']
  const ElIconLink: typeof import('@element-plus/icons-vue')['Link']
  const ElIconList: typeof import('@element-plus/icons-vue')['List']
  const ElIconLoading: typeof import('@element-plus/icons-vue')['Loading']
  const ElIconLocation: typeof import('@element-plus/icons-vue')['Location']
  const ElIconLocationFilled: typeof import('@element-plus/icons-vue')['LocationFilled']
  const ElIconLocationInformation: typeof import('@element-plus/icons-vue')['LocationInformation']
  const ElIconLock: typeof import('@element-plus/icons-vue')['Lock']
  const ElIconLollipop: typeof import('@element-plus/icons-vue')['Lollipop']
  const ElIconMagicStick: typeof import('@element-plus/icons-vue')['MagicStick']
  const ElIconMagnet: typeof import('@element-plus/icons-vue')['Magnet']
  const ElIconMale: typeof import('@element-plus/icons-vue')['Male']
  const ElIconManagement: typeof import('@element-plus/icons-vue')['Management']
  const ElIconMapLocation: typeof import('@element-plus/icons-vue')['MapLocation']
  const ElIconMedal: typeof import('@element-plus/icons-vue')['Medal']
  const ElIconMemo: typeof import('@element-plus/icons-vue')['Memo']
  const ElIconMenu: typeof import('@element-plus/icons-vue')['Menu']
  const ElIconMessage: typeof import('@element-plus/icons-vue')['Message']
  const ElIconMessageBox: typeof import('@element-plus/icons-vue')['MessageBox']
  const ElIconMic: typeof import('@element-plus/icons-vue')['Mic']
  const ElIconMicrophone: typeof import('@element-plus/icons-vue')['Microphone']
  const ElIconMilkTea: typeof import('@element-plus/icons-vue')['MilkTea']
  const ElIconMinus: typeof import('@element-plus/icons-vue')['Minus']
  const ElIconMoney: typeof import('@element-plus/icons-vue')['Money']
  const ElIconMonitor: typeof import('@element-plus/icons-vue')['Monitor']
  const ElIconMoon: typeof import('@element-plus/icons-vue')['Moon']
  const ElIconMoonNight: typeof import('@element-plus/icons-vue')['MoonNight']
  const ElIconMore: typeof import('@element-plus/icons-vue')['More']
  const ElIconMoreFilled: typeof import('@element-plus/icons-vue')['MoreFilled']
  const ElIconMostlyCloudy: typeof import('@element-plus/icons-vue')['MostlyCloudy']
  const ElIconMouse: typeof import('@element-plus/icons-vue')['Mouse']
  const ElIconMug: typeof import('@element-plus/icons-vue')['Mug']
  const ElIconMute: typeof import('@element-plus/icons-vue')['Mute']
  const ElIconMuteNotification: typeof import('@element-plus/icons-vue')['MuteNotification']
  const ElIconNoSmoking: typeof import('@element-plus/icons-vue')['NoSmoking']
  const ElIconNotebook: typeof import('@element-plus/icons-vue')['Notebook']
  const ElIconNotification: typeof import('@element-plus/icons-vue')['Notification']
  const ElIconOdometer: typeof import('@element-plus/icons-vue')['Odometer']
  const ElIconOfficeBuilding: typeof import('@element-plus/icons-vue')['OfficeBuilding']
  const ElIconOpen: typeof import('@element-plus/icons-vue')['Open']
  const ElIconOperation: typeof import('@element-plus/icons-vue')['Operation']
  const ElIconOpportunity: typeof import('@element-plus/icons-vue')['Opportunity']
  const ElIconOrange: typeof import('@element-plus/icons-vue')['Orange']
  const ElIconPaperclip: typeof import('@element-plus/icons-vue')['Paperclip']
  const ElIconPartlyCloudy: typeof import('@element-plus/icons-vue')['PartlyCloudy']
  const ElIconPear: typeof import('@element-plus/icons-vue')['Pear']
  const ElIconPhone: typeof import('@element-plus/icons-vue')['Phone']
  const ElIconPhoneFilled: typeof import('@element-plus/icons-vue')['PhoneFilled']
  const ElIconPicture: typeof import('@element-plus/icons-vue')['Picture']
  const ElIconPictureFilled: typeof import('@element-plus/icons-vue')['PictureFilled']
  const ElIconPictureRounded: typeof import('@element-plus/icons-vue')['PictureRounded']
  const ElIconPieChart: typeof import('@element-plus/icons-vue')['PieChart']
  const ElIconPlace: typeof import('@element-plus/icons-vue')['Place']
  const ElIconPlatform: typeof import('@element-plus/icons-vue')['Platform']
  const ElIconPlus: typeof import('@element-plus/icons-vue')['Plus']
  const ElIconPointer: typeof import('@element-plus/icons-vue')['Pointer']
  const ElIconPosition: typeof import('@element-plus/icons-vue')['Position']
  const ElIconPostcard: typeof import('@element-plus/icons-vue')['Postcard']
  const ElIconPouring: typeof import('@element-plus/icons-vue')['Pouring']
  const ElIconPresent: typeof import('@element-plus/icons-vue')['Present']
  const ElIconPriceTag: typeof import('@element-plus/icons-vue')['PriceTag']
  const ElIconPrinter: typeof import('@element-plus/icons-vue')['Printer']
  const ElIconPromotion: typeof import('@element-plus/icons-vue')['Promotion']
  const ElIconQuartzWatch: typeof import('@element-plus/icons-vue')['QuartzWatch']
  const ElIconQuestionFilled: typeof import('@element-plus/icons-vue')['QuestionFilled']
  const ElIconRank: typeof import('@element-plus/icons-vue')['Rank']
  const ElIconReading: typeof import('@element-plus/icons-vue')['Reading']
  const ElIconReadingLamp: typeof import('@element-plus/icons-vue')['ReadingLamp']
  const ElIconRefresh: typeof import('@element-plus/icons-vue')['Refresh']
  const ElIconRefreshLeft: typeof import('@element-plus/icons-vue')['RefreshLeft']
  const ElIconRefreshRight: typeof import('@element-plus/icons-vue')['RefreshRight']
  const ElIconRefrigerator: typeof import('@element-plus/icons-vue')['Refrigerator']
  const ElIconRemove: typeof import('@element-plus/icons-vue')['Remove']
  const ElIconRemoveFilled: typeof import('@element-plus/icons-vue')['RemoveFilled']
  const ElIconRight: typeof import('@element-plus/icons-vue')['Right']
  const ElIconScaleToOriginal: typeof import('@element-plus/icons-vue')['ScaleToOriginal']
  const ElIconSchool: typeof import('@element-plus/icons-vue')['School']
  const ElIconScissor: typeof import('@element-plus/icons-vue')['Scissor']
  const ElIconSearch: typeof import('@element-plus/icons-vue')['Search']
  const ElIconSelect: typeof import('@element-plus/icons-vue')['Select']
  const ElIconSell: typeof import('@element-plus/icons-vue')['Sell']
  const ElIconSemiSelect: typeof import('@element-plus/icons-vue')['SemiSelect']
  const ElIconService: typeof import('@element-plus/icons-vue')['Service']
  const ElIconSetUp: typeof import('@element-plus/icons-vue')['SetUp']
  const ElIconSetting: typeof import('@element-plus/icons-vue')['Setting']
  const ElIconShare: typeof import('@element-plus/icons-vue')['Share']
  const ElIconShip: typeof import('@element-plus/icons-vue')['Ship']
  const ElIconShop: typeof import('@element-plus/icons-vue')['Shop']
  const ElIconShoppingBag: typeof import('@element-plus/icons-vue')['ShoppingBag']
  const ElIconShoppingCart: typeof import('@element-plus/icons-vue')['ShoppingCart']
  const ElIconShoppingCartFull: typeof import('@element-plus/icons-vue')['ShoppingCartFull']
  const ElIconShoppingTrolley: typeof import('@element-plus/icons-vue')['ShoppingTrolley']
  const ElIconSmoking: typeof import('@element-plus/icons-vue')['Smoking']
  const ElIconSoccer: typeof import('@element-plus/icons-vue')['Soccer']
  const ElIconSoldOut: typeof import('@element-plus/icons-vue')['SoldOut']
  const ElIconSort: typeof import('@element-plus/icons-vue')['Sort']
  const ElIconSortDown: typeof import('@element-plus/icons-vue')['SortDown']
  const ElIconSortUp: typeof import('@element-plus/icons-vue')['SortUp']
  const ElIconStamp: typeof import('@element-plus/icons-vue')['Stamp']
  const ElIconStar: typeof import('@element-plus/icons-vue')['Star']
  const ElIconStarFilled: typeof import('@element-plus/icons-vue')['StarFilled']
  const ElIconStopwatch: typeof import('@element-plus/icons-vue')['Stopwatch']
  const ElIconSuccessFilled: typeof import('@element-plus/icons-vue')['SuccessFilled']
  const ElIconSugar: typeof import('@element-plus/icons-vue')['Sugar']
  const ElIconSuitcase: typeof import('@element-plus/icons-vue')['Suitcase']
  const ElIconSuitcaseLine: typeof import('@element-plus/icons-vue')['SuitcaseLine']
  const ElIconSunny: typeof import('@element-plus/icons-vue')['Sunny']
  const ElIconSunrise: typeof import('@element-plus/icons-vue')['Sunrise']
  const ElIconSunset: typeof import('@element-plus/icons-vue')['Sunset']
  const ElIconSwitch: typeof import('@element-plus/icons-vue')['Switch']
  const ElIconSwitchButton: typeof import('@element-plus/icons-vue')['SwitchButton']
  const ElIconSwitchFilled: typeof import('@element-plus/icons-vue')['SwitchFilled']
  const ElIconTakeawayBox: typeof import('@element-plus/icons-vue')['TakeawayBox']
  const ElIconTicket: typeof import('@element-plus/icons-vue')['Ticket']
  const ElIconTickets: typeof import('@element-plus/icons-vue')['Tickets']
  const ElIconTimer: typeof import('@element-plus/icons-vue')['Timer']
  const ElIconToiletPaper: typeof import('@element-plus/icons-vue')['ToiletPaper']
  const ElIconTools: typeof import('@element-plus/icons-vue')['Tools']
  const ElIconTop: typeof import('@element-plus/icons-vue')['Top']
  const ElIconTopLeft: typeof import('@element-plus/icons-vue')['TopLeft']
  const ElIconTopRight: typeof import('@element-plus/icons-vue')['TopRight']
  const ElIconTrendCharts: typeof import('@element-plus/icons-vue')['TrendCharts']
  const ElIconTrophy: typeof import('@element-plus/icons-vue')['Trophy']
  const ElIconTrophyBase: typeof import('@element-plus/icons-vue')['TrophyBase']
  const ElIconTurnOff: typeof import('@element-plus/icons-vue')['TurnOff']
  const ElIconUmbrella: typeof import('@element-plus/icons-vue')['Umbrella']
  const ElIconUnlock: typeof import('@element-plus/icons-vue')['Unlock']
  const ElIconUpload: typeof import('@element-plus/icons-vue')['Upload']
  const ElIconUploadFilled: typeof import('@element-plus/icons-vue')['UploadFilled']
  const ElIconUser: typeof import('@element-plus/icons-vue')['User']
  const ElIconUserFilled: typeof import('@element-plus/icons-vue')['UserFilled']
  const ElIconVan: typeof import('@element-plus/icons-vue')['Van']
  const ElIconVideoCamera: typeof import('@element-plus/icons-vue')['VideoCamera']
  const ElIconVideoCameraFilled: typeof import('@element-plus/icons-vue')['VideoCameraFilled']
  const ElIconVideoPause: typeof import('@element-plus/icons-vue')['VideoPause']
  const ElIconVideoPlay: typeof import('@element-plus/icons-vue')['VideoPlay']
  const ElIconView: typeof import('@element-plus/icons-vue')['View']
  const ElIconWallet: typeof import('@element-plus/icons-vue')['Wallet']
  const ElIconWalletFilled: typeof import('@element-plus/icons-vue')['WalletFilled']
  const ElIconWarnTriangleFilled: typeof import('@element-plus/icons-vue')['WarnTriangleFilled']
  const ElIconWarning: typeof import('@element-plus/icons-vue')['Warning']
  const ElIconWarningFilled: typeof import('@element-plus/icons-vue')['WarningFilled']
  const ElIconWatch: typeof import('@element-plus/icons-vue')['Watch']
  const ElIconWatermelon: typeof import('@element-plus/icons-vue')['Watermelon']
  const ElIconWindPower: typeof import('@element-plus/icons-vue')['WindPower']
  const ElIconZoomIn: typeof import('@element-plus/icons-vue')['ZoomIn']
  const ElIconZoomOut: typeof import('@element-plus/icons-vue')['ZoomOut']
  const ElLoading: typeof import('element-plus')['ElLoading']
  const ElMessage: typeof import('element-plus')['ElMessage']
  const ElMessageBox: typeof import('element-plus')['ElMessageBox']
  const ElNotification: typeof import('element-plus')['ElNotification']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app')['abortNavigation']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app')['addRouteMiddleware']
  const clearError: typeof import('../../node_modules/nuxt/dist/app')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app')['clearNuxtData']
  const computed: typeof import('vue')['computed']
  const copy: typeof import('../../utils/common')['copy']
  const createError: typeof import('../../node_modules/nuxt/dist/app')['createError']
  const customRef: typeof import('vue')['customRef']
  const debounce: typeof import('../../utils/common')['debounce']
  const deepClone: typeof import('../../utils/common')['deepClone']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app')['defineAppConfig']
  const defineAsyncComponent: typeof import('vue')['defineAsyncComponent']
  const defineComponent: typeof import('vue')['defineComponent']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app')['definePayloadReviver']
  const effect: typeof import('vue')['effect']
  const effectScope: typeof import('vue')['effectScope']
  const filterSpecial: typeof import('../../utils/common')['filterSpecial']
  const getCurrentInstance: typeof import('vue')['getCurrentInstance']
  const getCurrentScope: typeof import('vue')['getCurrentScope']
  const getToken: typeof import('../../utils/common')['getToken']
  const h: typeof import('vue')['h']
  const img: typeof import('../../utils/common')['img']
  const inject: typeof import('vue')['inject']
  const injectHead: typeof import('@unhead/vue')['injectHead']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app')['isPrerendered']
  const isProxy: typeof import('vue')['isProxy']
  const isReactive: typeof import('vue')['isReactive']
  const isReadonly: typeof import('vue')['isReadonly']
  const isRef: typeof import('vue')['isRef']
  const isShallow: typeof import('vue')['isShallow']
  const isUrl: typeof import('../../utils/common')['isUrl']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const language: typeof import('../../utils/language')['default']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app')['loadPayload']
  const markRaw: typeof import('vue')['markRaw']
  const moneyFormat: typeof import('../../utils/common')['moneyFormat']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app')['navigateTo']
  const nextTick: typeof import('vue')['nextTick']
  const onActivated: typeof import('vue')['onActivated']
  const onBeforeMount: typeof import('vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('vue')['onBeforeUpdate']
  const onDeactivated: typeof import('vue')['onDeactivated']
  const onErrorCaptured: typeof import('vue')['onErrorCaptured']
  const onMounted: typeof import('vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app')['onNuxtReady']
  const onRenderTracked: typeof import('vue')['onRenderTracked']
  const onRenderTriggered: typeof import('vue')['onRenderTriggered']
  const onScopeDispose: typeof import('vue')['onScopeDispose']
  const onServerPrefetch: typeof import('vue')['onServerPrefetch']
  const onUnmounted: typeof import('vue')['onUnmounted']
  const onUpdated: typeof import('vue')['onUpdated']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app')['preloadComponents']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app')['preloadRouteComponents']
  const provide: typeof import('vue')['provide']
  const proxyRefs: typeof import('vue')['proxyRefs']
  const reactive: typeof import('vue')['reactive']
  const readonly: typeof import('vue')['readonly']
  const ref: typeof import('vue')['ref']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app')['reloadNuxtApp']
  const request: typeof import('../../utils/request')['default']
  const resolveComponent: typeof import('vue')['resolveComponent']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app')['setResponseStatus']
  const shallowReactive: typeof import('vue')['shallowReactive']
  const shallowReadonly: typeof import('vue')['shallowReadonly']
  const shallowRef: typeof import('vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app')['showError']
  const storage: typeof import('../../utils/storage')['default']
  const t: typeof import('../../composables/useLang')['t']
  const test: typeof import('../../utils/test')['default']
  const toRaw: typeof import('vue')['toRaw']
  const toRef: typeof import('vue')['toRef']
  const toRefs: typeof import('vue')['toRefs']
  const triggerRef: typeof import('vue')['triggerRef']
  const unref: typeof import('vue')['unref']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app')['updateAppConfig']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app')['useAppConfig']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app')['useAsyncData']
  const useAttrs: typeof import('vue')['useAttrs']
  const useCaptcha: typeof import('../../composables/useCaptcha')['useCaptcha']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app')['useCookie']
  const useCssModule: typeof import('vue')['useCssModule']
  const useCssVars: typeof import('vue')['useCssVars']
  const useError: typeof import('../../node_modules/nuxt/dist/app')['useError']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app')['useFetch']
  const useHead: typeof import('@unhead/vue')['useHead']
  const useHeadSafe: typeof import('@unhead/vue')['useHeadSafe']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app')['useLazyFetch']
  const useLink: typeof import('vue-router')['useLink']
  const useLogin: typeof import('../../composables/useLogin')['useLogin']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app')['useNuxtData']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app')['useRequestFetch']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app')['useRequestHeaders']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app')['useRoute']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app')['useRuntimeConfig']
  const useSendSms: typeof import('../../composables/useSendSms')['useSendSms']
  const useSeoMeta: typeof import('@unhead/vue')['useSeoMeta']
  const useServerHead: typeof import('@unhead/vue')['useServerHead']
  const useServerHeadSafe: typeof import('@unhead/vue')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('@unhead/vue')['useServerSeoMeta']
  const useSlots: typeof import('vue')['useSlots']
  const useState: typeof import('../../node_modules/nuxt/dist/app')['useState']
  const useTransitionState: typeof import('vue')['useTransitionState']
  const watch: typeof import('vue')['watch']
  const watchEffect: typeof import('vue')['watchEffect']
  const withCtx: typeof import('vue')['withCtx']
  const withDirectives: typeof import('vue')['withDirectives']
  const withKeys: typeof import('vue')['withKeys']
  const withMemo: typeof import('vue')['withMemo']
  const withModifiers: typeof import('vue')['withModifiers']
  const withScopeId: typeof import('vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, InjectionKey, PropType, Ref, VNode } from 'vue'
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly ElIconAddLocation: UnwrapRef<typeof import('@element-plus/icons-vue')['AddLocation']>
    readonly ElIconAim: UnwrapRef<typeof import('@element-plus/icons-vue')['Aim']>
    readonly ElIconAlarmClock: UnwrapRef<typeof import('@element-plus/icons-vue')['AlarmClock']>
    readonly ElIconApple: UnwrapRef<typeof import('@element-plus/icons-vue')['Apple']>
    readonly ElIconArrowDown: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowDown']>
    readonly ElIconArrowDownBold: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowDownBold']>
    readonly ElIconArrowLeft: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowLeft']>
    readonly ElIconArrowLeftBold: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowLeftBold']>
    readonly ElIconArrowRight: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowRight']>
    readonly ElIconArrowRightBold: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowRightBold']>
    readonly ElIconArrowUp: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowUp']>
    readonly ElIconArrowUpBold: UnwrapRef<typeof import('@element-plus/icons-vue')['ArrowUpBold']>
    readonly ElIconAvatar: UnwrapRef<typeof import('@element-plus/icons-vue')['Avatar']>
    readonly ElIconBack: UnwrapRef<typeof import('@element-plus/icons-vue')['Back']>
    readonly ElIconBaseball: UnwrapRef<typeof import('@element-plus/icons-vue')['Baseball']>
    readonly ElIconBasketball: UnwrapRef<typeof import('@element-plus/icons-vue')['Basketball']>
    readonly ElIconBell: UnwrapRef<typeof import('@element-plus/icons-vue')['Bell']>
    readonly ElIconBellFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['BellFilled']>
    readonly ElIconBicycle: UnwrapRef<typeof import('@element-plus/icons-vue')['Bicycle']>
    readonly ElIconBottom: UnwrapRef<typeof import('@element-plus/icons-vue')['Bottom']>
    readonly ElIconBottomLeft: UnwrapRef<typeof import('@element-plus/icons-vue')['BottomLeft']>
    readonly ElIconBottomRight: UnwrapRef<typeof import('@element-plus/icons-vue')['BottomRight']>
    readonly ElIconBowl: UnwrapRef<typeof import('@element-plus/icons-vue')['Bowl']>
    readonly ElIconBox: UnwrapRef<typeof import('@element-plus/icons-vue')['Box']>
    readonly ElIconBriefcase: UnwrapRef<typeof import('@element-plus/icons-vue')['Briefcase']>
    readonly ElIconBrush: UnwrapRef<typeof import('@element-plus/icons-vue')['Brush']>
    readonly ElIconBrushFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['BrushFilled']>
    readonly ElIconBurger: UnwrapRef<typeof import('@element-plus/icons-vue')['Burger']>
    readonly ElIconCalendar: UnwrapRef<typeof import('@element-plus/icons-vue')['Calendar']>
    readonly ElIconCamera: UnwrapRef<typeof import('@element-plus/icons-vue')['Camera']>
    readonly ElIconCameraFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['CameraFilled']>
    readonly ElIconCaretBottom: UnwrapRef<typeof import('@element-plus/icons-vue')['CaretBottom']>
    readonly ElIconCaretLeft: UnwrapRef<typeof import('@element-plus/icons-vue')['CaretLeft']>
    readonly ElIconCaretRight: UnwrapRef<typeof import('@element-plus/icons-vue')['CaretRight']>
    readonly ElIconCaretTop: UnwrapRef<typeof import('@element-plus/icons-vue')['CaretTop']>
    readonly ElIconCellphone: UnwrapRef<typeof import('@element-plus/icons-vue')['Cellphone']>
    readonly ElIconChatDotRound: UnwrapRef<typeof import('@element-plus/icons-vue')['ChatDotRound']>
    readonly ElIconChatDotSquare: UnwrapRef<typeof import('@element-plus/icons-vue')['ChatDotSquare']>
    readonly ElIconChatLineRound: UnwrapRef<typeof import('@element-plus/icons-vue')['ChatLineRound']>
    readonly ElIconChatLineSquare: UnwrapRef<typeof import('@element-plus/icons-vue')['ChatLineSquare']>
    readonly ElIconChatRound: UnwrapRef<typeof import('@element-plus/icons-vue')['ChatRound']>
    readonly ElIconChatSquare: UnwrapRef<typeof import('@element-plus/icons-vue')['ChatSquare']>
    readonly ElIconCheck: UnwrapRef<typeof import('@element-plus/icons-vue')['Check']>
    readonly ElIconChecked: UnwrapRef<typeof import('@element-plus/icons-vue')['Checked']>
    readonly ElIconCherry: UnwrapRef<typeof import('@element-plus/icons-vue')['Cherry']>
    readonly ElIconChicken: UnwrapRef<typeof import('@element-plus/icons-vue')['Chicken']>
    readonly ElIconChromeFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['ChromeFilled']>
    readonly ElIconCircleCheck: UnwrapRef<typeof import('@element-plus/icons-vue')['CircleCheck']>
    readonly ElIconCircleCheckFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['CircleCheckFilled']>
    readonly ElIconCircleClose: UnwrapRef<typeof import('@element-plus/icons-vue')['CircleClose']>
    readonly ElIconCircleCloseFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['CircleCloseFilled']>
    readonly ElIconCirclePlus: UnwrapRef<typeof import('@element-plus/icons-vue')['CirclePlus']>
    readonly ElIconCirclePlusFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['CirclePlusFilled']>
    readonly ElIconClock: UnwrapRef<typeof import('@element-plus/icons-vue')['Clock']>
    readonly ElIconClose: UnwrapRef<typeof import('@element-plus/icons-vue')['Close']>
    readonly ElIconCloseBold: UnwrapRef<typeof import('@element-plus/icons-vue')['CloseBold']>
    readonly ElIconCloudy: UnwrapRef<typeof import('@element-plus/icons-vue')['Cloudy']>
    readonly ElIconCoffee: UnwrapRef<typeof import('@element-plus/icons-vue')['Coffee']>
    readonly ElIconCoffeeCup: UnwrapRef<typeof import('@element-plus/icons-vue')['CoffeeCup']>
    readonly ElIconCoin: UnwrapRef<typeof import('@element-plus/icons-vue')['Coin']>
    readonly ElIconColdDrink: UnwrapRef<typeof import('@element-plus/icons-vue')['ColdDrink']>
    readonly ElIconCollection: UnwrapRef<typeof import('@element-plus/icons-vue')['Collection']>
    readonly ElIconCollectionTag: UnwrapRef<typeof import('@element-plus/icons-vue')['CollectionTag']>
    readonly ElIconComment: UnwrapRef<typeof import('@element-plus/icons-vue')['Comment']>
    readonly ElIconCompass: UnwrapRef<typeof import('@element-plus/icons-vue')['Compass']>
    readonly ElIconConnection: UnwrapRef<typeof import('@element-plus/icons-vue')['Connection']>
    readonly ElIconCoordinate: UnwrapRef<typeof import('@element-plus/icons-vue')['Coordinate']>
    readonly ElIconCopyDocument: UnwrapRef<typeof import('@element-plus/icons-vue')['CopyDocument']>
    readonly ElIconCpu: UnwrapRef<typeof import('@element-plus/icons-vue')['Cpu']>
    readonly ElIconCreditCard: UnwrapRef<typeof import('@element-plus/icons-vue')['CreditCard']>
    readonly ElIconCrop: UnwrapRef<typeof import('@element-plus/icons-vue')['Crop']>
    readonly ElIconDArrowLeft: UnwrapRef<typeof import('@element-plus/icons-vue')['DArrowLeft']>
    readonly ElIconDArrowRight: UnwrapRef<typeof import('@element-plus/icons-vue')['DArrowRight']>
    readonly ElIconDCaret: UnwrapRef<typeof import('@element-plus/icons-vue')['DCaret']>
    readonly ElIconDataAnalysis: UnwrapRef<typeof import('@element-plus/icons-vue')['DataAnalysis']>
    readonly ElIconDataBoard: UnwrapRef<typeof import('@element-plus/icons-vue')['DataBoard']>
    readonly ElIconDataLine: UnwrapRef<typeof import('@element-plus/icons-vue')['DataLine']>
    readonly ElIconDelete: UnwrapRef<typeof import('@element-plus/icons-vue')['Delete']>
    readonly ElIconDeleteFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['DeleteFilled']>
    readonly ElIconDeleteLocation: UnwrapRef<typeof import('@element-plus/icons-vue')['DeleteLocation']>
    readonly ElIconDessert: UnwrapRef<typeof import('@element-plus/icons-vue')['Dessert']>
    readonly ElIconDiscount: UnwrapRef<typeof import('@element-plus/icons-vue')['Discount']>
    readonly ElIconDish: UnwrapRef<typeof import('@element-plus/icons-vue')['Dish']>
    readonly ElIconDishDot: UnwrapRef<typeof import('@element-plus/icons-vue')['DishDot']>
    readonly ElIconDocument: UnwrapRef<typeof import('@element-plus/icons-vue')['Document']>
    readonly ElIconDocumentAdd: UnwrapRef<typeof import('@element-plus/icons-vue')['DocumentAdd']>
    readonly ElIconDocumentChecked: UnwrapRef<typeof import('@element-plus/icons-vue')['DocumentChecked']>
    readonly ElIconDocumentCopy: UnwrapRef<typeof import('@element-plus/icons-vue')['DocumentCopy']>
    readonly ElIconDocumentDelete: UnwrapRef<typeof import('@element-plus/icons-vue')['DocumentDelete']>
    readonly ElIconDocumentRemove: UnwrapRef<typeof import('@element-plus/icons-vue')['DocumentRemove']>
    readonly ElIconDownload: UnwrapRef<typeof import('@element-plus/icons-vue')['Download']>
    readonly ElIconDrizzling: UnwrapRef<typeof import('@element-plus/icons-vue')['Drizzling']>
    readonly ElIconEdit: UnwrapRef<typeof import('@element-plus/icons-vue')['Edit']>
    readonly ElIconEditPen: UnwrapRef<typeof import('@element-plus/icons-vue')['EditPen']>
    readonly ElIconEleme: UnwrapRef<typeof import('@element-plus/icons-vue')['Eleme']>
    readonly ElIconElemeFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['ElemeFilled']>
    readonly ElIconElementPlus: UnwrapRef<typeof import('@element-plus/icons-vue')['ElementPlus']>
    readonly ElIconExpand: UnwrapRef<typeof import('@element-plus/icons-vue')['Expand']>
    readonly ElIconFailed: UnwrapRef<typeof import('@element-plus/icons-vue')['Failed']>
    readonly ElIconFemale: UnwrapRef<typeof import('@element-plus/icons-vue')['Female']>
    readonly ElIconFiles: UnwrapRef<typeof import('@element-plus/icons-vue')['Files']>
    readonly ElIconFilm: UnwrapRef<typeof import('@element-plus/icons-vue')['Film']>
    readonly ElIconFilter: UnwrapRef<typeof import('@element-plus/icons-vue')['Filter']>
    readonly ElIconFinished: UnwrapRef<typeof import('@element-plus/icons-vue')['Finished']>
    readonly ElIconFirstAidKit: UnwrapRef<typeof import('@element-plus/icons-vue')['FirstAidKit']>
    readonly ElIconFlag: UnwrapRef<typeof import('@element-plus/icons-vue')['Flag']>
    readonly ElIconFold: UnwrapRef<typeof import('@element-plus/icons-vue')['Fold']>
    readonly ElIconFolder: UnwrapRef<typeof import('@element-plus/icons-vue')['Folder']>
    readonly ElIconFolderAdd: UnwrapRef<typeof import('@element-plus/icons-vue')['FolderAdd']>
    readonly ElIconFolderChecked: UnwrapRef<typeof import('@element-plus/icons-vue')['FolderChecked']>
    readonly ElIconFolderDelete: UnwrapRef<typeof import('@element-plus/icons-vue')['FolderDelete']>
    readonly ElIconFolderOpened: UnwrapRef<typeof import('@element-plus/icons-vue')['FolderOpened']>
    readonly ElIconFolderRemove: UnwrapRef<typeof import('@element-plus/icons-vue')['FolderRemove']>
    readonly ElIconFood: UnwrapRef<typeof import('@element-plus/icons-vue')['Food']>
    readonly ElIconFootball: UnwrapRef<typeof import('@element-plus/icons-vue')['Football']>
    readonly ElIconForkSpoon: UnwrapRef<typeof import('@element-plus/icons-vue')['ForkSpoon']>
    readonly ElIconFries: UnwrapRef<typeof import('@element-plus/icons-vue')['Fries']>
    readonly ElIconFullScreen: UnwrapRef<typeof import('@element-plus/icons-vue')['FullScreen']>
    readonly ElIconGoblet: UnwrapRef<typeof import('@element-plus/icons-vue')['Goblet']>
    readonly ElIconGobletFull: UnwrapRef<typeof import('@element-plus/icons-vue')['GobletFull']>
    readonly ElIconGobletSquare: UnwrapRef<typeof import('@element-plus/icons-vue')['GobletSquare']>
    readonly ElIconGobletSquareFull: UnwrapRef<typeof import('@element-plus/icons-vue')['GobletSquareFull']>
    readonly ElIconGoldMedal: UnwrapRef<typeof import('@element-plus/icons-vue')['GoldMedal']>
    readonly ElIconGoods: UnwrapRef<typeof import('@element-plus/icons-vue')['Goods']>
    readonly ElIconGoodsFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['GoodsFilled']>
    readonly ElIconGrape: UnwrapRef<typeof import('@element-plus/icons-vue')['Grape']>
    readonly ElIconGrid: UnwrapRef<typeof import('@element-plus/icons-vue')['Grid']>
    readonly ElIconGuide: UnwrapRef<typeof import('@element-plus/icons-vue')['Guide']>
    readonly ElIconHandbag: UnwrapRef<typeof import('@element-plus/icons-vue')['Handbag']>
    readonly ElIconHeadset: UnwrapRef<typeof import('@element-plus/icons-vue')['Headset']>
    readonly ElIconHelp: UnwrapRef<typeof import('@element-plus/icons-vue')['Help']>
    readonly ElIconHelpFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['HelpFilled']>
    readonly ElIconHide: UnwrapRef<typeof import('@element-plus/icons-vue')['Hide']>
    readonly ElIconHistogram: UnwrapRef<typeof import('@element-plus/icons-vue')['Histogram']>
    readonly ElIconHomeFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['HomeFilled']>
    readonly ElIconHotWater: UnwrapRef<typeof import('@element-plus/icons-vue')['HotWater']>
    readonly ElIconHouse: UnwrapRef<typeof import('@element-plus/icons-vue')['House']>
    readonly ElIconIceCream: UnwrapRef<typeof import('@element-plus/icons-vue')['IceCream']>
    readonly ElIconIceCreamRound: UnwrapRef<typeof import('@element-plus/icons-vue')['IceCreamRound']>
    readonly ElIconIceCreamSquare: UnwrapRef<typeof import('@element-plus/icons-vue')['IceCreamSquare']>
    readonly ElIconIceDrink: UnwrapRef<typeof import('@element-plus/icons-vue')['IceDrink']>
    readonly ElIconIceTea: UnwrapRef<typeof import('@element-plus/icons-vue')['IceTea']>
    readonly ElIconInfoFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['InfoFilled']>
    readonly ElIconIphone: UnwrapRef<typeof import('@element-plus/icons-vue')['Iphone']>
    readonly ElIconKey: UnwrapRef<typeof import('@element-plus/icons-vue')['Key']>
    readonly ElIconKnifeFork: UnwrapRef<typeof import('@element-plus/icons-vue')['KnifeFork']>
    readonly ElIconLightning: UnwrapRef<typeof import('@element-plus/icons-vue')['Lightning']>
    readonly ElIconLink: UnwrapRef<typeof import('@element-plus/icons-vue')['Link']>
    readonly ElIconList: UnwrapRef<typeof import('@element-plus/icons-vue')['List']>
    readonly ElIconLoading: UnwrapRef<typeof import('@element-plus/icons-vue')['Loading']>
    readonly ElIconLocation: UnwrapRef<typeof import('@element-plus/icons-vue')['Location']>
    readonly ElIconLocationFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['LocationFilled']>
    readonly ElIconLocationInformation: UnwrapRef<typeof import('@element-plus/icons-vue')['LocationInformation']>
    readonly ElIconLock: UnwrapRef<typeof import('@element-plus/icons-vue')['Lock']>
    readonly ElIconLollipop: UnwrapRef<typeof import('@element-plus/icons-vue')['Lollipop']>
    readonly ElIconMagicStick: UnwrapRef<typeof import('@element-plus/icons-vue')['MagicStick']>
    readonly ElIconMagnet: UnwrapRef<typeof import('@element-plus/icons-vue')['Magnet']>
    readonly ElIconMale: UnwrapRef<typeof import('@element-plus/icons-vue')['Male']>
    readonly ElIconManagement: UnwrapRef<typeof import('@element-plus/icons-vue')['Management']>
    readonly ElIconMapLocation: UnwrapRef<typeof import('@element-plus/icons-vue')['MapLocation']>
    readonly ElIconMedal: UnwrapRef<typeof import('@element-plus/icons-vue')['Medal']>
    readonly ElIconMemo: UnwrapRef<typeof import('@element-plus/icons-vue')['Memo']>
    readonly ElIconMenu: UnwrapRef<typeof import('@element-plus/icons-vue')['Menu']>
    readonly ElIconMessage: UnwrapRef<typeof import('@element-plus/icons-vue')['Message']>
    readonly ElIconMessageBox: UnwrapRef<typeof import('@element-plus/icons-vue')['MessageBox']>
    readonly ElIconMic: UnwrapRef<typeof import('@element-plus/icons-vue')['Mic']>
    readonly ElIconMicrophone: UnwrapRef<typeof import('@element-plus/icons-vue')['Microphone']>
    readonly ElIconMilkTea: UnwrapRef<typeof import('@element-plus/icons-vue')['MilkTea']>
    readonly ElIconMinus: UnwrapRef<typeof import('@element-plus/icons-vue')['Minus']>
    readonly ElIconMoney: UnwrapRef<typeof import('@element-plus/icons-vue')['Money']>
    readonly ElIconMonitor: UnwrapRef<typeof import('@element-plus/icons-vue')['Monitor']>
    readonly ElIconMoon: UnwrapRef<typeof import('@element-plus/icons-vue')['Moon']>
    readonly ElIconMoonNight: UnwrapRef<typeof import('@element-plus/icons-vue')['MoonNight']>
    readonly ElIconMore: UnwrapRef<typeof import('@element-plus/icons-vue')['More']>
    readonly ElIconMoreFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['MoreFilled']>
    readonly ElIconMostlyCloudy: UnwrapRef<typeof import('@element-plus/icons-vue')['MostlyCloudy']>
    readonly ElIconMouse: UnwrapRef<typeof import('@element-plus/icons-vue')['Mouse']>
    readonly ElIconMug: UnwrapRef<typeof import('@element-plus/icons-vue')['Mug']>
    readonly ElIconMute: UnwrapRef<typeof import('@element-plus/icons-vue')['Mute']>
    readonly ElIconMuteNotification: UnwrapRef<typeof import('@element-plus/icons-vue')['MuteNotification']>
    readonly ElIconNoSmoking: UnwrapRef<typeof import('@element-plus/icons-vue')['NoSmoking']>
    readonly ElIconNotebook: UnwrapRef<typeof import('@element-plus/icons-vue')['Notebook']>
    readonly ElIconNotification: UnwrapRef<typeof import('@element-plus/icons-vue')['Notification']>
    readonly ElIconOdometer: UnwrapRef<typeof import('@element-plus/icons-vue')['Odometer']>
    readonly ElIconOfficeBuilding: UnwrapRef<typeof import('@element-plus/icons-vue')['OfficeBuilding']>
    readonly ElIconOpen: UnwrapRef<typeof import('@element-plus/icons-vue')['Open']>
    readonly ElIconOperation: UnwrapRef<typeof import('@element-plus/icons-vue')['Operation']>
    readonly ElIconOpportunity: UnwrapRef<typeof import('@element-plus/icons-vue')['Opportunity']>
    readonly ElIconOrange: UnwrapRef<typeof import('@element-plus/icons-vue')['Orange']>
    readonly ElIconPaperclip: UnwrapRef<typeof import('@element-plus/icons-vue')['Paperclip']>
    readonly ElIconPartlyCloudy: UnwrapRef<typeof import('@element-plus/icons-vue')['PartlyCloudy']>
    readonly ElIconPear: UnwrapRef<typeof import('@element-plus/icons-vue')['Pear']>
    readonly ElIconPhone: UnwrapRef<typeof import('@element-plus/icons-vue')['Phone']>
    readonly ElIconPhoneFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['PhoneFilled']>
    readonly ElIconPicture: UnwrapRef<typeof import('@element-plus/icons-vue')['Picture']>
    readonly ElIconPictureFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['PictureFilled']>
    readonly ElIconPictureRounded: UnwrapRef<typeof import('@element-plus/icons-vue')['PictureRounded']>
    readonly ElIconPieChart: UnwrapRef<typeof import('@element-plus/icons-vue')['PieChart']>
    readonly ElIconPlace: UnwrapRef<typeof import('@element-plus/icons-vue')['Place']>
    readonly ElIconPlatform: UnwrapRef<typeof import('@element-plus/icons-vue')['Platform']>
    readonly ElIconPlus: UnwrapRef<typeof import('@element-plus/icons-vue')['Plus']>
    readonly ElIconPointer: UnwrapRef<typeof import('@element-plus/icons-vue')['Pointer']>
    readonly ElIconPosition: UnwrapRef<typeof import('@element-plus/icons-vue')['Position']>
    readonly ElIconPostcard: UnwrapRef<typeof import('@element-plus/icons-vue')['Postcard']>
    readonly ElIconPouring: UnwrapRef<typeof import('@element-plus/icons-vue')['Pouring']>
    readonly ElIconPresent: UnwrapRef<typeof import('@element-plus/icons-vue')['Present']>
    readonly ElIconPriceTag: UnwrapRef<typeof import('@element-plus/icons-vue')['PriceTag']>
    readonly ElIconPrinter: UnwrapRef<typeof import('@element-plus/icons-vue')['Printer']>
    readonly ElIconPromotion: UnwrapRef<typeof import('@element-plus/icons-vue')['Promotion']>
    readonly ElIconQuartzWatch: UnwrapRef<typeof import('@element-plus/icons-vue')['QuartzWatch']>
    readonly ElIconQuestionFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['QuestionFilled']>
    readonly ElIconRank: UnwrapRef<typeof import('@element-plus/icons-vue')['Rank']>
    readonly ElIconReading: UnwrapRef<typeof import('@element-plus/icons-vue')['Reading']>
    readonly ElIconReadingLamp: UnwrapRef<typeof import('@element-plus/icons-vue')['ReadingLamp']>
    readonly ElIconRefresh: UnwrapRef<typeof import('@element-plus/icons-vue')['Refresh']>
    readonly ElIconRefreshLeft: UnwrapRef<typeof import('@element-plus/icons-vue')['RefreshLeft']>
    readonly ElIconRefreshRight: UnwrapRef<typeof import('@element-plus/icons-vue')['RefreshRight']>
    readonly ElIconRefrigerator: UnwrapRef<typeof import('@element-plus/icons-vue')['Refrigerator']>
    readonly ElIconRemove: UnwrapRef<typeof import('@element-plus/icons-vue')['Remove']>
    readonly ElIconRemoveFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['RemoveFilled']>
    readonly ElIconRight: UnwrapRef<typeof import('@element-plus/icons-vue')['Right']>
    readonly ElIconScaleToOriginal: UnwrapRef<typeof import('@element-plus/icons-vue')['ScaleToOriginal']>
    readonly ElIconSchool: UnwrapRef<typeof import('@element-plus/icons-vue')['School']>
    readonly ElIconScissor: UnwrapRef<typeof import('@element-plus/icons-vue')['Scissor']>
    readonly ElIconSearch: UnwrapRef<typeof import('@element-plus/icons-vue')['Search']>
    readonly ElIconSelect: UnwrapRef<typeof import('@element-plus/icons-vue')['Select']>
    readonly ElIconSell: UnwrapRef<typeof import('@element-plus/icons-vue')['Sell']>
    readonly ElIconSemiSelect: UnwrapRef<typeof import('@element-plus/icons-vue')['SemiSelect']>
    readonly ElIconService: UnwrapRef<typeof import('@element-plus/icons-vue')['Service']>
    readonly ElIconSetUp: UnwrapRef<typeof import('@element-plus/icons-vue')['SetUp']>
    readonly ElIconSetting: UnwrapRef<typeof import('@element-plus/icons-vue')['Setting']>
    readonly ElIconShare: UnwrapRef<typeof import('@element-plus/icons-vue')['Share']>
    readonly ElIconShip: UnwrapRef<typeof import('@element-plus/icons-vue')['Ship']>
    readonly ElIconShop: UnwrapRef<typeof import('@element-plus/icons-vue')['Shop']>
    readonly ElIconShoppingBag: UnwrapRef<typeof import('@element-plus/icons-vue')['ShoppingBag']>
    readonly ElIconShoppingCart: UnwrapRef<typeof import('@element-plus/icons-vue')['ShoppingCart']>
    readonly ElIconShoppingCartFull: UnwrapRef<typeof import('@element-plus/icons-vue')['ShoppingCartFull']>
    readonly ElIconShoppingTrolley: UnwrapRef<typeof import('@element-plus/icons-vue')['ShoppingTrolley']>
    readonly ElIconSmoking: UnwrapRef<typeof import('@element-plus/icons-vue')['Smoking']>
    readonly ElIconSoccer: UnwrapRef<typeof import('@element-plus/icons-vue')['Soccer']>
    readonly ElIconSoldOut: UnwrapRef<typeof import('@element-plus/icons-vue')['SoldOut']>
    readonly ElIconSort: UnwrapRef<typeof import('@element-plus/icons-vue')['Sort']>
    readonly ElIconSortDown: UnwrapRef<typeof import('@element-plus/icons-vue')['SortDown']>
    readonly ElIconSortUp: UnwrapRef<typeof import('@element-plus/icons-vue')['SortUp']>
    readonly ElIconStamp: UnwrapRef<typeof import('@element-plus/icons-vue')['Stamp']>
    readonly ElIconStar: UnwrapRef<typeof import('@element-plus/icons-vue')['Star']>
    readonly ElIconStarFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['StarFilled']>
    readonly ElIconStopwatch: UnwrapRef<typeof import('@element-plus/icons-vue')['Stopwatch']>
    readonly ElIconSuccessFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['SuccessFilled']>
    readonly ElIconSugar: UnwrapRef<typeof import('@element-plus/icons-vue')['Sugar']>
    readonly ElIconSuitcase: UnwrapRef<typeof import('@element-plus/icons-vue')['Suitcase']>
    readonly ElIconSuitcaseLine: UnwrapRef<typeof import('@element-plus/icons-vue')['SuitcaseLine']>
    readonly ElIconSunny: UnwrapRef<typeof import('@element-plus/icons-vue')['Sunny']>
    readonly ElIconSunrise: UnwrapRef<typeof import('@element-plus/icons-vue')['Sunrise']>
    readonly ElIconSunset: UnwrapRef<typeof import('@element-plus/icons-vue')['Sunset']>
    readonly ElIconSwitch: UnwrapRef<typeof import('@element-plus/icons-vue')['Switch']>
    readonly ElIconSwitchButton: UnwrapRef<typeof import('@element-plus/icons-vue')['SwitchButton']>
    readonly ElIconSwitchFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['SwitchFilled']>
    readonly ElIconTakeawayBox: UnwrapRef<typeof import('@element-plus/icons-vue')['TakeawayBox']>
    readonly ElIconTicket: UnwrapRef<typeof import('@element-plus/icons-vue')['Ticket']>
    readonly ElIconTickets: UnwrapRef<typeof import('@element-plus/icons-vue')['Tickets']>
    readonly ElIconTimer: UnwrapRef<typeof import('@element-plus/icons-vue')['Timer']>
    readonly ElIconToiletPaper: UnwrapRef<typeof import('@element-plus/icons-vue')['ToiletPaper']>
    readonly ElIconTools: UnwrapRef<typeof import('@element-plus/icons-vue')['Tools']>
    readonly ElIconTop: UnwrapRef<typeof import('@element-plus/icons-vue')['Top']>
    readonly ElIconTopLeft: UnwrapRef<typeof import('@element-plus/icons-vue')['TopLeft']>
    readonly ElIconTopRight: UnwrapRef<typeof import('@element-plus/icons-vue')['TopRight']>
    readonly ElIconTrendCharts: UnwrapRef<typeof import('@element-plus/icons-vue')['TrendCharts']>
    readonly ElIconTrophy: UnwrapRef<typeof import('@element-plus/icons-vue')['Trophy']>
    readonly ElIconTrophyBase: UnwrapRef<typeof import('@element-plus/icons-vue')['TrophyBase']>
    readonly ElIconTurnOff: UnwrapRef<typeof import('@element-plus/icons-vue')['TurnOff']>
    readonly ElIconUmbrella: UnwrapRef<typeof import('@element-plus/icons-vue')['Umbrella']>
    readonly ElIconUnlock: UnwrapRef<typeof import('@element-plus/icons-vue')['Unlock']>
    readonly ElIconUpload: UnwrapRef<typeof import('@element-plus/icons-vue')['Upload']>
    readonly ElIconUploadFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['UploadFilled']>
    readonly ElIconUser: UnwrapRef<typeof import('@element-plus/icons-vue')['User']>
    readonly ElIconUserFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['UserFilled']>
    readonly ElIconVan: UnwrapRef<typeof import('@element-plus/icons-vue')['Van']>
    readonly ElIconVideoCamera: UnwrapRef<typeof import('@element-plus/icons-vue')['VideoCamera']>
    readonly ElIconVideoCameraFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['VideoCameraFilled']>
    readonly ElIconVideoPause: UnwrapRef<typeof import('@element-plus/icons-vue')['VideoPause']>
    readonly ElIconVideoPlay: UnwrapRef<typeof import('@element-plus/icons-vue')['VideoPlay']>
    readonly ElIconView: UnwrapRef<typeof import('@element-plus/icons-vue')['View']>
    readonly ElIconWallet: UnwrapRef<typeof import('@element-plus/icons-vue')['Wallet']>
    readonly ElIconWalletFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['WalletFilled']>
    readonly ElIconWarnTriangleFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['WarnTriangleFilled']>
    readonly ElIconWarning: UnwrapRef<typeof import('@element-plus/icons-vue')['Warning']>
    readonly ElIconWarningFilled: UnwrapRef<typeof import('@element-plus/icons-vue')['WarningFilled']>
    readonly ElIconWatch: UnwrapRef<typeof import('@element-plus/icons-vue')['Watch']>
    readonly ElIconWatermelon: UnwrapRef<typeof import('@element-plus/icons-vue')['Watermelon']>
    readonly ElIconWindPower: UnwrapRef<typeof import('@element-plus/icons-vue')['WindPower']>
    readonly ElIconZoomIn: UnwrapRef<typeof import('@element-plus/icons-vue')['ZoomIn']>
    readonly ElIconZoomOut: UnwrapRef<typeof import('@element-plus/icons-vue')['ZoomOut']>
    readonly ElLoading: UnwrapRef<typeof import('element-plus')['ElLoading']>
    readonly ElMessage: UnwrapRef<typeof import('element-plus')['ElMessage']>
    readonly ElMessageBox: UnwrapRef<typeof import('element-plus')['ElMessageBox']>
    readonly ElNotification: UnwrapRef<typeof import('element-plus')['ElNotification']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['abortNavigation']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['addRouteMiddleware']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['clearNuxtData']>
    readonly computed: UnwrapRef<typeof import('vue')['computed']>
    readonly copy: UnwrapRef<typeof import('../../utils/common')['copy']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['createError']>
    readonly customRef: UnwrapRef<typeof import('vue')['customRef']>
    readonly debounce: UnwrapRef<typeof import('../../utils/common')['debounce']>
    readonly deepClone: UnwrapRef<typeof import('../../utils/common')['deepClone']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('vue')['defineComponent']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['definePayloadReviver']>
    readonly effect: UnwrapRef<typeof import('vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('vue')['effectScope']>
    readonly filterSpecial: UnwrapRef<typeof import('../../utils/common')['filterSpecial']>
    readonly getCurrentInstance: UnwrapRef<typeof import('vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('vue')['getCurrentScope']>
    readonly getToken: UnwrapRef<typeof import('../../utils/common')['getToken']>
    readonly h: UnwrapRef<typeof import('vue')['h']>
    readonly img: UnwrapRef<typeof import('../../utils/common')['img']>
    readonly inject: UnwrapRef<typeof import('vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('@unhead/vue')['injectHead']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('vue')['isShallow']>
    readonly isUrl: UnwrapRef<typeof import('../../utils/common')['isUrl']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly language: UnwrapRef<typeof import('../../utils/language')['default']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['loadPayload']>
    readonly markRaw: UnwrapRef<typeof import('vue')['markRaw']>
    readonly moneyFormat: UnwrapRef<typeof import('../../utils/common')['moneyFormat']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('vue')['onBeforeUpdate']>
    readonly onDeactivated: UnwrapRef<typeof import('vue')['onDeactivated']>
    readonly onErrorCaptured: UnwrapRef<typeof import('vue')['onErrorCaptured']>
    readonly onMounted: UnwrapRef<typeof import('vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['onNuxtReady']>
    readonly onRenderTracked: UnwrapRef<typeof import('vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('vue')['onServerPrefetch']>
    readonly onUnmounted: UnwrapRef<typeof import('vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('vue')['onUpdated']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['preloadRouteComponents']>
    readonly provide: UnwrapRef<typeof import('vue')['provide']>
    readonly proxyRefs: UnwrapRef<typeof import('vue')['proxyRefs']>
    readonly reactive: UnwrapRef<typeof import('vue')['reactive']>
    readonly readonly: UnwrapRef<typeof import('vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('vue')['ref']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['reloadNuxtApp']>
    readonly request: UnwrapRef<typeof import('../../utils/request')['default']>
    readonly resolveComponent: UnwrapRef<typeof import('vue')['resolveComponent']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['showError']>
    readonly storage: UnwrapRef<typeof import('../../utils/storage')['default']>
    readonly t: UnwrapRef<typeof import('../../composables/useLang')['t']>
    readonly test: UnwrapRef<typeof import('../../utils/test')['default']>
    readonly toRaw: UnwrapRef<typeof import('vue')['toRaw']>
    readonly toRef: UnwrapRef<typeof import('vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('vue')['toRefs']>
    readonly triggerRef: UnwrapRef<typeof import('vue')['triggerRef']>
    readonly unref: UnwrapRef<typeof import('vue')['unref']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['updateAppConfig']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useAppConfig']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useAsyncData']>
    readonly useAttrs: UnwrapRef<typeof import('vue')['useAttrs']>
    readonly useCaptcha: UnwrapRef<typeof import('../../composables/useCaptcha')['useCaptcha']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useCookie']>
    readonly useCssModule: UnwrapRef<typeof import('vue')['useCssModule']>
    readonly useCssVars: UnwrapRef<typeof import('vue')['useCssVars']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useError']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useFetch']>
    readonly useHead: UnwrapRef<typeof import('@unhead/vue')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('@unhead/vue')['useHeadSafe']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('vue-router')['useLink']>
    readonly useLogin: UnwrapRef<typeof import('../../composables/useLogin')['useLogin']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useNuxtData']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useRequestFetch']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useRequestHeaders']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useRoute']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useRuntimeConfig']>
    readonly useSendSms: UnwrapRef<typeof import('../../composables/useSendSms')['useSendSms']>
    readonly useSeoMeta: UnwrapRef<typeof import('@unhead/vue')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('@unhead/vue')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('@unhead/vue')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('@unhead/vue')['useServerSeoMeta']>
    readonly useSlots: UnwrapRef<typeof import('vue')['useSlots']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app')['useState']>
    readonly useTransitionState: UnwrapRef<typeof import('vue')['useTransitionState']>
    readonly watch: UnwrapRef<typeof import('vue')['watch']>
    readonly watchEffect: UnwrapRef<typeof import('vue')['watchEffect']>
    readonly withCtx: UnwrapRef<typeof import('vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('vue')['withScopeId']>
  }
}
