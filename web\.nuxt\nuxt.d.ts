// Generated by nuxi
/// <reference types="@element-plus/nuxt" />
/// <reference types="nuxt-windicss" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="nuxt" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/vue-shim.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="nuxt-config-schema" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}
