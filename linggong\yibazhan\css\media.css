@media screen and (min-width: 900px) {
	*{
		font-size: 12px;
	}
	body{ 
	/*	min-width: 1300px;*/min-width: 1260px
	}
	.header{
		height: 70px;
	/*	min-width: 1300px;*/min-width: 1260px
	}
	.header .header_logo{
		margin-top: 15px;
		margin-left: 2%;
	}
	.header_logo img{
		height: 39px;
	}
	.header_logo span{
		font-size: 0.875em;
		padding-left: 12px;
		margin-left: 12px;
	}
	.header_pc{
		margin-left: 20px;
	}
	.header_pc .header_topic{
		margin: 0 12px;
	}
	.header_word01 a{
		font-size: 0.9375em;
		line-height: 70px;
	}
	.login{
		float: right;
		width: 7%;
	}
	.login span{
		font-size: 0.9375em;
		line-height: 70px;
	}
	.login i{
		font-size: 1em;
	}
	.header_tip{
		margin-top: 25px;
	}
	.tell_tu img,.earth_tu img{
		height: 20px;
	}
	.code_tu01,.code_tu02{
		height: 20px;
	}
	.tell_tu,.code_tu,.earth_tu{
		margin-right: 15px;
	}
	.earth_tu span{
		font-size: 0.9375em;
		line-height: 20px;
		margin-left: 10px;
	}
	.tip_box{
		top: 46px;
		padding: 1em;
		border-radius: 0.2em;
	}
	.tip_box section{
		padding: 0.1em 0.3em;
	}
	.tip_box1{
		top: 46px;
		padding: 0.5em;
		border-radius: 0.2em;
	}
	.tip_box1 section{
		padding: 0.5em 0.9em;
		text-align: center;
		line-height: 1em;
	}
	.tip_box1 section span{
	    font-size:1.6em;
	    color:#fff;
	}
	.world{
	    padding: 5em 0;
	    top: 70px;
	}
	.world p{
	    font-size: 1.375em;
	}
	.world span{
	    font-size: 2.25em;
	    margin: 0.2em 0 1.2em;
	}
	.world section{
	    font-size: 1.25em;
	}
	.world img{
	    width: 40em;
	    right: 5%;
	}
	.searcher{
		height: 70px;
		padding: 0 12px;
		margin-right: 10px;
	}
	.searcher input{
		font-size: 0.9375em;
		width: 7em;
		margin-right: 1em;
	}
	.header_pho,.header_menu{
		display: none;
	}
	.banner_pic{
		height: 40em;
	}
	.banner_bg img{
		width: 37.5em;
		right: 28%; 
	}
	.container{
		width: 68%;
	}
	.banner_word{
		width: 25%;
		left: 16%;
	}
	.banner_word p{
		font-size: 2.6em;
	}
	.banner_word span{
		font-size: 1.25em;
		margin: 1.5em 0 3em;
	}
	.banner_word button{
		width: 7.5em;
		line-height: 2.6em;
		border-radius: 0.3em;
	}
	.form_bg{
		width: 19%;
		position: absolute; 
		top: 55%; 
		transform: translateY(-50%); 
		right: 16%;
		z-index: 99;
	}
	.banner_form{
		border-radius: 0.3em;
	}
	.form_title{
		height: 3.6em;
	}
	.form_title li:first-child{
		border-top-left-radius: 0.3em;
	}
	.form_title li:last-child{ 
		border-top-right-radius: 0.3em;
	}
	.form_title li p{
		line-height: 3.7em;
	}
	.form_title .form_title_on{
		border-top-left-radius: 0.3em;
		border-top-right-radius: 0.3em;
	}
	.form_title .form_title_on p{ 
		font-size: 1.125em;
		line-height: 4em;
	}
	.form_title .form_title_on p::after{
		width: 36%;
		height: 0.25em;
		border-radius: 0.2em;
	}
	.form_ul{
		padding: 2em 8%;
	}
	.form_ul button{
		font-size: 1.125em;
		line-height: 3em;
		height: 3em;
		border-radius: 0.2em;
	}
	.edit_title{
		margin-bottom: 1.3em;
	}
	.edit_text{
		margin-bottom: 1.3em;
		border-radius: 0.2em;
	}
	.edit_text1{
		padding-left: 20%;
	}
	.edit_text1 img{
		width: 1.3em;
	}
	.edit_text input{
		height: 3em;
		line-height: 3em;
	}
	.edit_text1 input{
		width: 90%;
	}
	.edit_text2 input{ 
		width: 50%;
		padding: 0 5%;
		margin-left: 35%;
	}
	
	.edit_text2 input::-webkit-outer-spin-button,
	.edit_text2 input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
	.edit_text2 input[type="number"]{
  -moz-appearance: textfield;
}

	.edit_text2 span{
		right: 6%;
	}
	.edit_tip1{
		margin-top: 1.5em;
	}
	.edit_tip2{
		margin-top: 1.5em;
		height: 3em;
		line-height: 3em;
	}
	.wide{ 
		width: 50%;
	}
	.wide1{ 
		width: 65%;
	}
	.wide2{ 
		width: 35%;
	}
	.wide3{ 
		width: 60%;
	}
	.wide4{ 
		width: 28%;
	}
	.banner_adv{
		position: absolute; 
		left: 0; 
		bottom: -6.5em;
		z-index: 9;
	}
	.banner_adv ul{
		padding: 2em 3%;
		border-radius: 0.4em;
		box-shadow: 0 10px 12px #f2f2f2;
	}
	.banner_adv img{
		width: 3.4375em;
	}
	.banner_adv section{
		margin-left: 1.2em;
	}
	.banner_adv p{
		font-size: 1.25em;
	}
	.banner_adv span{
		font-size: 0.9375em;
		margin-top: 0.8em;
		line-height: 1.5em;
	}
	.product{
		padding: 6em 0 6.2em;
	}
	.product_title{
		margin-bottom: 4.2em;
	}
	.product_title h2{
		font-size: 2.25em;
	}
	.product_title p{
		font-size: 2.25em;
	}
	.product_t{
		margin-top: 7em;
	}
	.pro_line{
		width: 2em;
		height: 0.3em;
		border-radius: 0.3em;
		margin-top: 3.5em;
	}
	.serve{ 
		border: solid 1px #e1e1e1;
	}
	.serve_ul{
		float: left;
		width: 77%;
	}
	.serve_ul li{
		padding: 2em 2.2em; 
	}
	.serve_ul li:nth-child(-n+2){ 
		width: 50%;
	}
	.serve_ul li:nth-child(n+3){ 
		border-bottom: 0;
	}
	.serve_ul li:nth-child(3){ 
		width: 33%;
	}
	.serve_ul li:nth-child(4){ 
		width: 34%;
	}
	.serve_ul li:nth-child(5){ 
		width: 33%;
	}
	.serve_ull{ 
		font-size: 1.5em;
		font-weight:bold;
	}
	.serve_ul p{
		line-height: 1.6em;
		margin: 1.2em 0;
	}
	.serve_ul .serve_icon{
		width: 2em;
	}
	.serve_ul .serve_img{
		top: 2em;
		right: 2em; 
	}
	.serve_db{
		width: 23%;
		position: absolute; 
		top: 0; 
		right: 0; 
		height: 100%; 
		z-index: 9;
		padding: 1.5em 2% 0;
	}
	.serve_db11{
		font-size: 1.25em;
		font-weight:bold;
	}
	.serve_db1 a{
		font-size: 0.875em;
	}
	.serve_db2{ 
		padding: 0 5%;
		margin: 1.2em 0 1em;
		border-radius: 0.2em;
	}
	.serve_db2 input{
		font-size: 0.875em;
		height: 2.8em;
	}
	.serve_db2 img{
		width: 0.8em;
	}
	.serve_db3 li{
		font-size: 0.875em;
		padding-left: 6%;
		height: 3.2em;
		line-height: 3.2em;
	}
	.serve_db3 li::before{
		width: 0.2em;
		height: 0.2em;
	}
	.solve{
		padding: 1em 0 0;
	}
	.solve_title{
		width: 22%;
		padding: 1em 0;
	}
	.solve_title li{
		padding: 0 1.5em;
	}
	.solve_title .solve_item{
		padding: 1em 0;
	}
	.solve_title img{
		height: 20px;
	}
	.solve_title p{
		font-size: 0.9375em;
		line-height: 20px;
		margin-left: 1.5em;
	}
	.solve_line{
		bottom: -2.2em;
	}
	.solve_con{
		width: 71%;
	}
	.solve_ul .solve_infor{
		padding: 2.8em 0 0 3%;
	}
	.solve_infor11{
		font-size: 1.625em;
	}
	.solve_infor1 span{
		width: 1.8em;
		height: 0.3em;
		border-radius: 0.3em;
		margin: 1.5em 0;
	}
	.solve_infor1 p{
		height: 14.5em;
		line-height: 2.4em;
	}
	.solve_infor2{
		padding: 2em 0;
	}
	.solve_infor2 p{
		line-height: 2em;
		margin-right: 0.1em;
	}
	.solve_infor2 span{
		padding: 0 1em;
		line-height: 2em;
		border-radius: 2em;
		margin-right: 0.2em;
	}
	.solve_infor2 a{
		right: 0; 
		top: 50%; 
		transform: translateY(-50%);
		line-height: 2.4em;
		padding: 0 1.6em;
	}
	.news_a{
		width: 32%;
		padding-right: 4%;
	}
	.news_b{
		width: 43%;
		padding-right: 4%;
	}
	.news_c{
		width: 25%;
	}
	.news_a_list{
		margin-top: 1.3em;
	}
	.news_title h3,.news_title h3{
		font-size: 1.125em;
		line-height: 3.6em;
	}
	.news_title h1::after{ 
		top: -0.1em; 
		height: 0.3em;
	}
	.news_title span{
		font-size: 0.875em;
	}
	.news_title img{
		margin-left: 0.5em;
	}
	.news_a_list li{
		margin-bottom: 1.2em;
	}
	.news_a_pic{
		width: 42%;
		height: 5em;
		border-radius: 0.2em;
	}
	.news_a_w{
		width: 51%;
		font-size: 0.9375em;
	}
	.news_a_t{
		width: 51%;
		font-size: 0.8125em;
	}
	.news_tab{
		margin-bottom: 0.9em;
		display: flex; 
		align-items: center; 
		justify-content: space-between;
	}
	.news_tab p{
		font-size: 1.125em;
		line-height: 3.6em;
	}
	.news_b_list1{
		font-size: 1.25em;
		margin-bottom: 1.2em;
	}
	.news_b_list2w{
		margin-top: 1.3em;
		padding-left: 3%;
	}
	.news_b_list2w::before{
		width: 0.2em;
		height: 0.2em;
	}
	.news_b_list2w p{
		width: 85%;
	}
	.news_b_list2w span{ display:none;}
	.news_c{
		padding: 0 1.8%;
	}
	.news_c_list{
		margin-top: 0.4em;
	}
	.news_c_list li{
		line-height: 2.4em;
	}
	.news_c_list span{
		width: 1.3em;
		font-size: 0.875em;
		margin-left: 0.2em;
	}
	.news_c_list p{
		font-size: 0.875em;
		margin-left: 0.2em;
	}
	.support li:nth-child(1){
		width: 35%;
	}
	.support li:nth-child(2){
		width: 65%;
	}
	.support1{
		height: 10.5em;
		padding: 2em 2em 0;
	}
	.support1 h3{
		font-size: 1.625em;
	}
	.support_icon{
		margin-top: 3em;
	}
	.support_icon span{
		font-size: 0.875em;
	}
	.support_icon img{
		margin-left: 0.3em;
	}
	.support2{
		height: 21em;
		padding: 2.8em 2.5em 0;
		border-left: solid 1px #dadde5; 
		background: url(../../yibazhan/images/sup_icon03.png) right center no-repeat;
		background-size: auto 90%;
	}
	.support2 h3{
		font-size: 1.875em;
	}
	.support2 span{
		width: 2em;
		height: 0.3em;
		border-radius: 0.2em;
		margin: 1.5em 0 1.8em;
	}
	.support2 p{
		width: 65%;
		line-height: 1.8em;
	}
	.support2_btn{
		margin-top: 2.5em;
	}
	.support2_btn button{
		width: 7.2em;
		line-height: 2.2em;
		border-radius: 0.2em;
	}
	.support2_btn .support2_btn02{
		margin-left: 1.2em;
	}
	.partner1{
		width: 25%;
		border-right: solid 1px #dadde5;
	}
	.partner2{
		width: 70%;
	}
	.partner1 li{
		margin-bottom: 2.4em;
	}
	.partner1 img{
		float: left;
		margin-top: 0.3em;
	}
	.partner1 section{
		float: left;
		margin-left: 0.8em;
	}
	.partner11{
		font-size: 1.625em;
	}
	.partner1 p{
		font-size: 0.875em;
	}
	.partner2 li{
		width: 16.5%;
		height: 5em;
		margin-top: 2em;
		text-align: right;
	}
	.partner2 img{
		width: 7.5em;
	}
	
	.hzhb li{
		width: 16.5%;
		height: 5em;
		margin-top: 2em;
		text-align: left;
		float:left;
	}
	.hzhb img{
		width: 10em;
	}
	
	
	.touch{
		padding: 2.5em 0;
	}
	.touch h5{
		font-size: 1.625em;
	}
	.touch p{
		margin-top: 1em;
	}
	.touch button{
		line-height: 2.6em;
		padding: 0 2em;
		border-radius: 0.2em;
	}
	.touch button.pc{ display:block;}
	.touch button.mob{ display:none;}
	.pro_form_pic img{
		height: 14.25em;
	}
	.footer01{
		padding: 4em 0 3em;
	}
	.footer01 section{
		margin-left: 0.8em;
		justify-content: space-between;
	}
	.footer01 h6{
		font-size: 1.125em;
	}
	.footer01 p{
		font-size: 0.875em;
	}
	.footer01 img{
		width: 3.2em;
	}
	.footer02{
		padding: 3.5em 0;
	}
	.footer02a li{
		margin-right: 3em;
	}
	.footer02a li:last-child{ 
		margin-right: 0;
	}
	.footer02a p{
		margin-bottom: 2.4em;
	}
	.footer02a span{
		font-size: 0.875em;
		margin-bottom: 1.2em;
	}
	.footer02b h6{
		margin-bottom: 2.4em;
	}
	.footer02b p{
		font-size: 0.875em;
		margin-bottom: 1em;
	}
	.footer02b span{
		font-size: 1em;
	}
	.footer02b_img{
		padding-top: 0.7em;
		margin-bottom: 2.2em;
	}
	.footer02b_img img{
		height: 7em;
		border-radius: 0.2em;
	}
	.footer02b_img img:nth-child(1){
		margin-right: 1.5em;
	}
	.footer03{
		padding: 3em 0;
	}
	.footer03a img{
		height: 2.4em;
		margin-right: 2.4em;
	}
	.footer03a span{
		line-height: 2.4em;
		margin-right: 1em;
		padding-right: 1em;
	}
	.footer03a select{
		font-size: 0.875em;
		width: 12em;
		height: 2.8em;
		line-height: 2.8em;
		border-radius: 0.4em;
		padding: 0 0.5%;
	}
	.footer03b{
		margin-top: 1.5em;
	}
	.footer03b_l{
		width: 52%;
	}
	.footer03b_l p{
		font-size: 0.875em;
		margin-bottom: 1em;
	}
	.footer03b_r img{
		height: 2.5em;
	}
	.picture_pic{
		height: 26em;
	}
	.picture_pic_mob{
		display: none;
	}
	.picture_word{
		left: 16%;
		top: 42%; 
	}
	.picture_word p{
		font-size: 3em;
	}
	.picture_word span{
		font-size: 1.5em;
		margin-top: 0.8em;
	}
	.picture_word section{
		font-size: 1.25em;
		margin-top: 1em;
	}
	.brief_general{
		margin-top: -0.9em;
	}
	.brief_l,.brief_r{
		width: 50%;
	}
	.brief_b1{
		padding-right: 3%;
	}
	.brief_b2{
		padding-left: 3%;
	}
	.brief_b2_1{
		padding-left: 10%;
	}
	.brief_h1{
		font-size: 1.25em;
		margin-bottom: 2.2em;
	}
	.brief_word{
		line-height: 2em;
	}
	
	.magTop-1{
	    margin-top:-3.3em;
	}

	.brief_btn{
		margin-bottom: 2.5em;
	}
	.brief_btn button{
		font-size: 1.125em;
		padding: 0.3em 1.2em;
		border-radius: 3em;
	}
	.question li{
		width: 23.5%;
		padding: 2em 3%;
	}
	.question img,.questioner img{
		height: 38px;
	}
	.questioner li{
		width: 31%;
		padding: 2em 3%;
	}
	.question_title p{
		font-size: 1em;
		line-height: 38px;
		margin-left: 1em;
	}
	.question section,.questioner section{
		font-size: 0.875em;
		line-height: 1.8em;
		margin-top: 1.6em;
	}
	.question_caption p{
		font-size: 1.125em;
		margin: 1.3em 0 0;
	}
	.plan_t{ 
		font-size: 1.25em;
	}
	.plan_ul{
		margin-top: 1.3em;
	}
	.plan_ul li:nth-child(1){
		width: 52%;
		padding-right: 8%;
	}
	.plan_ul li:nth-child(2){
		width: 48%;
		padding-right: 3%;
	}
	.plan_ul span{
		font-size: 0.9375em;
		padding: 0.2em 0.6em;
		border-radius: 0.3em;
		margin-bottom: 1em;
	}
	.plan_ul p{
		font-size: 0.875em;
		line-height: 1.8em;
	}
	.plan_p1{
		font-size: 1.125em;
		padding-left: 1.2em;
		margin-bottom: 1.5em;
	}
	.plan_p2{
		line-height: 2em;
	}
	.plan_p1::after{
		width: 0.3em;
		border-radius: 0.2em;
	}
	.brief_l .plan_w:nth-child(1){
		margin-bottom: 2.5em;
	}
	.plan_pic{
		padding-top: 3em;
	}
	.plan_pic1{
		margin-top: -0.8em;
	}
	.product_tip{
		font-size: 1.125em;
		margin-top: 1.2em;
		line-height: 1.6em;
	}
	.product_tip01{
		font-size: 1em;
		line-height: 1.8em;
	}
	.brief_l .plan_h{
		padding-right: 8%;
	}
	.brief_r .plan_h{
		padding-left: 8%;
	}
	.plan_h li:nth-child(1){
		margin-bottom: 3.2em;
	}
	.product_btn{
		font-size: 1.125em;
		border-radius: 2em;
		padding: 0.3em 1.3em;
	}
	.analytical{
		margin-top: -1.5em;
	}
	.analytical_ul li{
		width: 48%;
		border-radius: 0.3em;
	}
	.analytical_ul_other li{
	    width: 32%;
	    margin-right: 2%;
	}
	.analytical_ul li:nth-child(1){
		margin-right: 4%;
	}
	.analytical_ul_other li:last-child{
	     margin-right: 0;
	}
	.analytical_line{
		padding: 1.8em 6%;
	}
	.analytical_ul button{
		padding: 0.4em 1.2em;
		border-radius: 1em;
	}
	.analytical_ul_other button{
		padding: 0.4em 1.2em;
		border-radius: 1em;
	}
	.analytical_ul h3{
		margin: 0.3em 0 1.5em;
	}
	.analytical_ul_other h3{
		margin: 0.3em 0 1.5em;
	}
	.analytical_h01{
		min-height: 7.8em;
	}
	.analytical_h02{
		min-height: 10.5em;
	}
	.analytical_h03{
		min-height: 15em;
	}
	.analytical_h04{
		min-height: 6em;
	}
	.analytical_h05{
		min-height: 16em;
	}
	.analytical_h06{
		min-height: 9em;
	}
	.analytical_p p{
		line-height: 2.6em;
	}
	.analytical_p .an_step{
		margin-bottom: 1.2em;
	}
	.analytical_line .an_small{
		font-size: 0.875em;
	}
	.analytical_line .an_small span{
		font-size: 1em;
	}
	.analytical_line .an_br{
		margin-top: 0.6em;
	}
	.analytical_line .an_bot{
		margin-bottom: 1em;
	}
	.analytical_btn{
		margin-top: 3.5em;
	}
	.analytical_btn p{
		width: 55%;
		border-radius: 2em;
		padding: 0.8em 0;
	}
	.analytical_item,.analytical_account{
		padding: 2em 6%;
	}
	.analytical_item p{
		padding-left: 5%;
		line-height: 2.5em;
	}
	.analytical_item p::after{
		width: 6px;
		height: 6px;
		top: 1.1em;
	}
	.analytical_account1{
		width: 50%;
		padding-right: 8%;
	}
	.analytical_account2{
		width: 50%;
		padding-left: 8%;
	}
	.analytical_account_h{
		line-height: 2.4em;
	}
	.virtue li{
		width: 22%;
		margin-right: 4%;
	}
	.virtue .virtue_pic{
		width: 30%;
	}
	.virtue_pic img{
		height: 42px;
	}
	.virtue .virtue_word{
		width: 70%;
	}
	.virtue_word p{
		font-size: 1em;
		margin-bottom: 0.8em;
	}
	.virtue_word span{
		font-size: 0.875em;
		line-height: 1.5em;
	}
	.client{
		padding: 2em;
		background-size: 41px;
		background-position: left 1.5em top 1.5em;
	}
	.client_top{
		margin-top: 7em;
	}
	.client_space{
		padding-top: 3em;
	}
	.client_button_prev{
		margin-right: 2em;
	}
	.client_word{
		height: 13em;
	}
	.client_word p{
	    font-size: 1em;
		-webkit-line-clamp: 8;
		line-height: 1.8em;
	}
	.client_title{
		margin-top: 2em;
	}
	.client_title img{
		height: 35px;
	}
	.client_title p{
		font-size: 1em;
		line-height: 20px;
		width: 50%;
	}
	.pro_form{
		padding: 3em 0;
	}
	.pro_form_title h5{
		font-size: 2em;
		margin-right: 1.3em;
	}
	.pro_form_title p{ 
		display: inline-block;
	} 
	.pro_form_input{
		width: 80%;
		margin-top: 2.3em;
	}
	.pro_form_input input{
		width: 41.5%;
		margin-right: 1.5%;
		height: 3em;
		line-height: 3em;
		padding: 0 3%;
		border-radius: 0.2em;
	}
	.pro_form_input button{
		width: 14%;
		height: 3em;
		border-radius: 0.2em;
	}
	.pro_form_pic{
		top: -1em;
	}
	.plan_chart{
		margin-bottom: 1.5em;
	}
	.plan_chart td{
		padding: 1em 0;
	}
	.brief_pic li{
		width: 24%;
		margin-left: 1.2%;
		padding: 2em 0;
		border-radius: 0.3em;
	}
	.brief_pic li:first-child{ 
		margin-left: 0;
	}
	.brief_pic li img{
		width: 6.2em;
	}
	.brief_pic3 img{
	    margin-top: 1em;
	    margin-bottom: -0.9em;
	}
	.brief_ot{
		padding-left: 10%;
	}
	.brief_ot_1{
		font-size: 1.125em;
		margin-bottom: 1.5em;
	}
	.brief_ot_2 li{
		padding-left: 4%;
		line-height: 1.6em;
		margin-bottom: 1em;
	}
	.brief_ot_2 li::after{
		width: 5px;
		height: 5px;
		top: 0.7em;
	}
	.brief_ot_wide{
		padding-right: 20%;
	}
	.adapt li{
		width: 18.4%;
		margin-right: 2%;
		height: 16em;
		border-radius: 0.3em;
	}
	.adapt li:last-child{ 
		margin-right: 0;
	}
	.adapt_word{
		left: 2em;
		top: 1.8em;
	}
	.adapt_wordd{
		font-size: 1.25em;
		font-weight:bold;
	}
	.adapt_textt{
	    font-size: 16px;
	    font-weight:bold;
	}
	.adapt_liucheg{
	    font-size: 16px;
	    font-weight:bold;
	}
	.adapt_word span{
		width: 1.3em;
		height: 0.3em;
		border-radius: 0.2em;
		margin: 1.2em 0;
	}
	.adapt_icon{
		right: 2em;
		bottom: 1.8em;
	}
	.adapt_icon img{
		width: 3.43em;
	}
	.path li{
		width: 27%;
		padding: 1em 0;
		margin-bottom: 2em;
	}
	.path li:nth-child(4n){
		width: 19%;
	}
	.path p{
		font-size: 1.5em;
		line-height: 30px;
	}
	.pathh{
		font-size: 1.125em;
		line-height: 30px;
		font-weight:bold;
	}
	.path span{
		font-size: 0.875em;
		margin-top: 0.5em;
	}
	.pathp{
		font-size: 0.875em;
		line-height: 2em;
		margin-top: 0.5em;
		color: #787b85;
	}
	.path section{
		margin-left: 1.2em;
	}
	.path li{
		background-image: url(../../yibazhan/images/arrow_tu.png);
		background-repeat:no-repeat;
		background-size: 1.375em;
		background-position: right 20% top 1.8em;
	}
	.path li:nth-child(4n){
		background: none;
	}
	.path li:last-child{
		background: none;
	}
	.path li section{
		width: 50%;
	}
	.path li:nth-child(4n) section{
		width: 75%;
	}
	.brief_card li{
		width: 48%;
		margin-right: 4%;
		padding: 2em 6%;
	}
	.brief_card img{
		float: left;
		width: 2.5em;
		margin-top: 0.2em;
	}
	.brief_card p{
		float: right;
		width: 65%;
		line-height: 1.6em;
	}
	.brief_card li:last-child{ 
		margin-right: 0;
	}
	.policy li{
		width: 33.3%;
		height: 18.5em;
		padding: 2.3em 3.5% 0;
		border-bottom: solid 1px #e1e1e1; 
		border-right: solid 1px #e1e1e1;
	}
	.policy li:nth-child(3n){ 
		border-right: 0;
	}
	.policy li:nth-last-child(-n+3){ 
		border-bottom: 0;
	}
	.policyy{
		font-size: 1.125em;
		padding-left: 1em;
		margin-bottom: 1.5em;
	}
	.policyy::after{
		width: 0.3em;
		height: 90%;
		border-radius: 0.3em;
		top: 8%; 
	}
	.policy p{
		font-size: 0.9375em;
		padding-left: 1em;
		line-height: 1.6em;
		margin-bottom: 0.5em;
	}
	.policy p::after{
		width: 3px;
		height: 3px;
		top: 0.7em;
	}
	.theory{
		padding-top: 2em;
	}
	.scene{
		margin-top: -1.5em;
	}
	.scene li{
		width: 18%;
		margin-right: 2.5%;
		padding: 2em 0;
	}
	.scene img{
		height: 24px;
	}
	.scene span{
		line-height: 24px;
		margin-left: 0.5em;
	}
	.scene li:nth-child(5n){ 
		margin-right: 0;
	}
	.receipt{ 
		position: absolute; 
		right: 0; 
		bottom: 0; 
		z-index: 99;
	}
	.receipt li{
		padding: 0.4em 1.3em;
		border-radius: 2em;
		margin-left: 1em;
	}
	.receipt-bill li{
		border-radius: 0.2em;
		width: 23.8%;
		height: 3.5em;
		line-height: 3.5em;
		margin-right: 1.6%;
		margin-bottom: 1.3em;
	}
	.receipt-bill li:nth-child(4n){
		margin-right: 0;
	}
	.receipt-bill li:nth-last-child(-n+4){ 
		margin-bottom: 0;
	}
	.path_btn{
		margin-bottom: 4em;
	}
	.path_btn li{
		padding-bottom: 1.5em;
		margin-right: 3em;
	}
	.trade01 li{
	    width: 18%;
	    margin-right: 2.5%;
	    margin-bottom: 2em;
	}
	.trade01 .trade_pic{
	    height: 8em;
	    padding: 1.5em;
	}
	.trade01 li:nth-child(5n){
	    margin-right: 0;
	}
	.trade01 li .trade_icon01{
	    right: 1.2em;
	    top: 1.3em;
	}
	.trade_icon01 img{
	    width: 2.25em;
	}
	.trade01 li .trade_icon02{
	    left: 1.2em;
	    bottom: 1.3em;
	}
	.trade_icon02 img{
	    width: 1.5em;
	}
	.trade02 li{
	    width: 31%;
	    margin-right: 3.5%;
	    margin-bottom: 2em;
	}
	.trade02 li:nth-child(3n){
	    margin-right: 0;
	}
	.trade02 li .trade_icon01{
	    right: 1.5em;
	    top: 1.5em;
	}
	.trade02 .trade_pic{
	    height: 11em;
	    padding: 2em 2em 0;
	}
	.trade02 .trade_pic p{
	    font-size: 1.25em;
	}
	.trade02 .trade_pic section{
	    margin-top: 1.5em;
	    line-height: 1.8em;
	}
	.guest_title{ 
		overflow: hidden;
		position: absolute; 
		right: 0; 
		bottom: 0; 
		z-index: 99;
	}
	.guest_title li{
		float: left; 
		line-height: 2.5em;
		margin-left: 2.5em;
	}
	.guest_pic{
		border-radius: 0.1em;
		width: 11%;
		height: 4em;
		line-height: 4em;
		margin-right: 1.7%;
		margin-bottom: 1.3em;
	}
	.guest_pic img{
		max-width: 80%;
	}
	.guest_ul li .guest_pic:nth-child(8n){ 
		margin-right: 0;
	}
	.packet_btn{
		border-radius: 0.2em;
		padding: 0.6em 1.6em;
	}
	.clause li{
		width: 32%;
		margin-right: 2%;
		margin-bottom: 2.8em;
	}
	.clause_pic{
		width: 35%;
		height: 6em;
		border-radius: 0.3em;
	}
	.clause li:nth-child(3n){ 
		margin-right: 0;
	}
	.clause_word{
		width: 60%;
		height: 6em;
	}
	.clause_wordbiaoti{
		font-size: 1.125em;
		line-height: 1.6em;
		font-weight:bold;
	}
	.clause_word p{
		font-size: 0.875em;
	}
	.tax-ul li{
		width: 50%;
	}
	.tax-ul li:nth-child(2n+1){
		padding-right: 5%;
	}
	.tax-ul li:nth-child(2n){
		padding-left: 5%;
	}
	.tax-ul li:nth-last-child(-n+2){
		margin-bottom: 0;
	}
	.tax-title img{
		height: 51px;
	}
	.tax-titlee{
		font-size: 1.125em;
		line-height: 51px;
		margin-left: 1.2em;
	}
	.tax-titleee {
    font-weight: bold;
    font-size: 16px;
}
	.tax-titlxx {
	font-weight: bold;
	font-size: 16px;
	}
	.tax-p{
		font-size: 0.9375em;
		line-height: 1.8em;
		margin-top: 1.6em;
		height: 11em;
	}
	.preference li{
		border-radius: 0.4em;
		width: 47.5%;
		margin-right: 2.4%;
		margin-bottom:2em;
	}
	.preference li:last-child{
		margin-right: 0;
	}
	.preference1{
		padding: 1.2em 8%;
	}
	.preference1 button{
		font-size: 1.125em;
		border-radius: 3em;
		padding: 0.4em 1.5em;
	}
	.preference1 span{
		font-size: 2.8em;
		right: 6%;
	}
	.preference2{
		padding: 0 10%;
		height: 25em;
	}
	.preference21{
		font-size: 0.9375em;
		margin: 1.8em 0 1.2em;
		font-weight:bold;
	}
	.preference2 p{
		font-size: 0.9375em;
		line-height: 1.6em;
	}
	.resource_bg{
		padding: 2em 0;
	}
	.resource_l{
		width: 40%;
	}
	.resource_r{
		width: 42%;
		position: absolute; 
		right: 0; 
		top: 0;
		z-index: 9;
	}
	.resource_w p{
		font-size: 1.125em;
		line-height: 2em;
		margin-bottom: 2em;
	}
	.resource_w section{
		font-size: 1.125em;
		padding-top: 2em;
	}
	.principle_l{
		padding: 2em 5em 2em 2.3em;
	}
	.principle_l button{
		padding: 0.3em 1.3em;
		border-radius: 2em;
	}
	.principle_l ul{
		margin-top: 2.5em;
	}
	.principle_l li{
		margin-bottom: 1.2em;
		padding-left: 1.5em;
	}
	.principle_l li:last-child{
		margin-bottom: 0;
	}
	.principle_l li::after{
		width: 6px;
		height: 6px;
		top: 50%; 
		transform: translateY(-50%);
	}
	.principle_r{
		width: 55%;
	}
	.suit li{
		width: 31%;
		margin-right: 3.5%;
		padding: 2.5em 3% 0;
		height: 15.5em;
	}
	.suit li:last-child{
		margin-right: 0;
	}
	.suitt{
		font-size: 1.125em;
		font-weight:bold;
	}
	.suit i{
		width: 1.8em;
		height: 0.3em;
		border-radius: 0.3em;
		margin: 1.6em 0;
	}
	.suit p{
		line-height: 1.8em;
	}
	.suit span{
		font-size: 3em;
		top: 0.4em; 
		right: 6%;
	}
	.platform_word{
		font-size: 1.125em;
		line-height: 2.2em;
		margin-top: -1em;
	}
	.platform_title h3{
		font-size: 1.875em;
	}
	.platform_title p{
		font-size: 1.5em;
		margin-top: 0.3em;
	}
	.platform_it{
		margin-top: 5em;
	}
	.platform_tab{
		margin-top: 2.6em;
	}
	.platform_tab li{
		padding: 0.8em;
		border-radius: 0.4em;
	}
	.platform_tab .on{
		padding: 1.3em 1.6em;
	}
	.platform_tab p{
		font-size: 1.25em;
	}
	.platform_tab span{
		font-size: 0.875em;
		margin-top: 0.5em;
	}
	.platform_tab i{ 
		width: 1.2em;
		height: 0.3em;
		border-radius: 0.5em;
		right: 9%; 
		top: 1.8em;
	}
	.abut li{
		width: 48%;
		margin-right: 4%;
		border-radius: 0.3em;
		padding: 4em 4% 0 0;
		height: 16em;
	}
	.abut li:nth-child(2n){
		margin-right: 0;
	}
	.abut .abut_pic{
		width: 30%;
	}
	.abut .abut_w{
		width: 70%;
	}
	.abut_pic img{
		width: 4.5em;
	}
	.abut_w h4{
		font-size: 1.25em;
	}
	.abut_w p{
		line-height: 1.8em;
		margin-top: 1.6em;
	}
	.count_l,.count_r{
		width: 42%;
	}
	.count{
		padding: 8em 0;
	}
	.count_title{
		padding-bottom: 2.4em;
	}
	.count_title h2{
		font-size: 2.25em;
		margin-bottom: 1em;
	}
	.count_ul{
		margin-top: 4em;
	}
	.count_ul li{
		margin-bottom: 3.5em;
	}
	.count_ul li:nth-child(2n+1){
		width: 58%;
	}
	.count_ul li:nth-child(2n){
		width: 42%;
	}
	.count_ul p{
	 /*	font-size: 1.25em;*/
		margin: 0.8em 0 0.5em;
	}
	.count_ul span{
		font-size: 0.9375em;
		line-height: 1.6em;
	}
	.counter{
		border-radius: 0.8em;
		padding: 2.8em 3em 2em;
	}
	.counter button{
		font-size: 1.25em;
		border-radius: 1em;
		padding: 0.4em 0;
	}
	.counter1{
		margin-top: 2.5em;
	}
	.counter1 li{
		padding: 0 6%;
		margin-bottom: 1.2em;
		border-radius: 0.4em;
	}
	.counter1 span{
		line-height: 3em;
	}
	.counter1 input{
		width: 60%;
		height: 3em;
		line-height: 3em;
		padding: 0 12% 0 6%;
	}
		
	.counter1 input::-webkit-outer-spin-button,
	.counter1 input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
	.counter1 input[type="number"]{
  -moz-appearance: textfield;
}
	.counter1 i{
		right: 6%;
	}
	.counter2{
		margin-top: 2em;
	}
	.counter2 li{
		width: 100%;
		padding: 0.6em 0;
	}
	.counter2 p{
		line-height: 2em;
	}
	.counter2 span{
		line-height: 2em;
	}
	.handbook{
		padding: 6em 0 3em;
	}
	.handbook_title{ font-size:2.2em; margin-bottom:2.4em; }
	.handbook li{
		width: 48%;
		padding: 2em 3% 2em 0;
		margin-right: 4%;
		margin-bottom: 3.5em;
	}
	.handbook li:nth-child(2n){
		margin-right: 0;
	}
	.handbook_l{
		width: 30%;
	}
	.handbook_l img{
		height: 3.5em;
	}
	.handbook_r{
		width: 70%;
	}
	.handbook_r h3{
		font-size: 1.125em;
	}
	.handbook_r p{
		font-size: 0.875em;
		margin: 1.6em 0;
	}
	.handbook_r span{
		font-size: 0.875em;
	}
	.superiority li{
    	width: 22%;
    	height: 18em;
    	margin-right: 4%;
    	border-radius: 0.2em;
        position: relative;
        background: linear-gradient(to bottom, #f9fafd,#f0f2f7);
    }
    .superiority li:nth-child(4n){
    	margin-right: 0;
    }
    .superiority01{
        width: 80%;
    	left: 15%;
    	bottom: 2.5em;
    }
    .superiority011{
    	font-size: 1.25em;
    	font-weight:bold;
    }
    .superiority01 span{
    	font-size: 0.9375em;
    	margin-top: 0.8em;
    }
    .superiority02{
    	right: 2em;
    	top: 2em;
    }
    .superiority02 img{ 
    	width: 2.875em;
    }
	.banner_record{
		position: absolute; 
		left: 0; 
		bottom: -7em;
		z-index: 9;
	}
	.banner_record ul{
		padding: 3em 5.5%;
		border-radius: 0.4em;
	}
	.banner_record section{
		font-size: 2.25em;
	}
	.banner_record i{
		font-size: 0.75em;
		line-height: 1em;
		margin-left: 0.1em;
	}
	.banner_record p{
		margin-top: 0.3em;
	}
	.about_l{
		width: 48%;
	}
	.about_l img{
	   height: 43em;
	    width:100%;
	    object-fit:cover;
	}
	
	.about_p{
		line-height: 1.8em;
	}
	.about_r{
		width: 42%;
		margin-top: 4em;
	}
	.about_tu{
		/*height: 25em;*/
		margin-top:3em;
	}
	.settle li{
		width: 45%;
	}
	.settle li:nth-child(2n+1){
		float: left;
	}
	.settle li:nth-child(2n){
		float: right;
	}
	.settle_pic{
		width: 18%;
	}
	.settle_pic img{
		height: 51px;
	}
	.settle_w{
		width: 82%;
		height: 10.5em;
	}
	.settle li:last-child .settle_w{
		height: auto;
	}
	.settle_w p{
		font-size: 1.25em;
		margin-bottom: 0.6em;
	}
	.settle_w span{
		font-size: 0.9375em;
		line-height: 2em;
	}
	.certificate_w{
		width: 68%;
		line-height: 2em;
	}
	.certificate1{
		width: 45%;
	}
	.certificate2{
		width: 55%;
	}
	.cert_button img{
		width: 1.5em;
	}
	.cert_button_prev{
		margin-right: 2em;
	}
	.collaborate1{
		width: 42%;
	}
	.collaborate1 p{
		font-size: 2.25em;
		line-height: 1.6em;
	}
	.collaborate1 span{
		width: 2em;
		height: 0.25em;
		border-radius: 0.5em;
		margin: 1.5em 0;
	}
	.collaborate2{
		width: 58%;
		border-radius: 0.2em;
		padding: 2em 3%;
	}
	
	/*about 灰底表单*/
	.collaborate2-1{
		width: 58%;
		border-radius: 0.2em;
		padding: 2em 3%;
	}
	.collaborate3a input{
		width: 48%;
		border-radius: 0.3em;
		height: 3.6em;
		line-height: 3.6em;
		margin-right: 4%;
		padding: 0 3%;
	}
	
	.collaborate3a input:nth-child(2n){
		margin-right: 0;
	}
	.collaborate3b{
		margin: 1.8em 0;
	}
	.collaborate3b textarea{
		height: 9em;
		padding: 1.2em 3%;
		border-radius: 0.3em;
	}
	
		/*about 白底表单*/
	.collaborate2a input{
		width: 48%;
		border-radius: 0.3em;
		height: 3.6em;
		line-height: 3.6em;
		margin-right: 4%;
		padding: 0 3%;
	}
	
	.collaborate2a input:nth-child(2n){
		margin-right: 0;
	}
	.collaborate2b{
		margin: 1.8em 0;
	}
	.collaborate2b textarea{
		height: 9em;
		padding: 1.2em 3%;
		border-radius: 0.3em;
	}
	
	.collaborate2c{ 
		text-align: right;
	}
	.collaborate2c button{
		width: 12em;
		line-height: 3em;
		height: 3em;
		border-radius: 0.3em;
	}
	.collaborate3{ 
		position: absolute; 
		left: 0; 
		bottom: 0; 
		z-index: 9;
	}
	.collaborate3 p{
		font-size: 1.25em;
		margin-bottom: 0.2em;
	}
	.collaborate3 span{
		font-size: 2.5em;
	}
	.contact{
		padding-bottom: 2em;
	}
	.contact1{
		width: 55%;
		height: 30em;
	}
	.contact2{
		width: 40%;
		position: absolute; 
		/*top: 46%; 
		transform: translateY(-50%); */
	    top:0;
		right: 0; 
		z-index: 9;
	}
	.contact2 li{
		margin-bottom: 2em;
	}
	.contact2 li:last-child{
		margin-bottom: 0;
	}
	.contact2 span{
		margin-top: 0.8em;
	}
	.content_l{
		width: 66%;
	}
	.content_r{
		width: 24%;
	}
	.information_title{
		padding: 3.5em 0;
	}
	.information_title ul{ 
		overflow: hidden;
	}
	.information_title li{
		float: left; 
		margin-right: 2.5em;
	}
	.information_list li{
		padding: 2.5em 0;
		border-bottom: solid 1px #e1e1e1;
	}
	.information_pic{
		width: 22%;
		height: 8em;
		border-radius: 0.2em;
	}
	.information_w{
		width: 72%;
		height: 8em;
	}
	.information_w h3{
		font-size: 1.25em;
	}
	.information_w p{
		font-size: 0.875em;
		line-height: 1.8em;
		margin-top: 1em;
	}
	.information_w span{
		font-size: 0.875em;
	}
	.sidebar{
		margin-bottom: 3em;
	}
	.sidebar_title{
		line-height: 3.6em;
	}
	.sidebar_title span{
		font-size: 1.125em;
		border-top: solid 3px #336de2;
	}
	.hotspot{
		margin-top: 0.1em;
	}
	.hotspot li{
		font-size: 0.875em;
		padding-left: 5%;
		line-height: 2.8em;
	}
	.hotspot li::after{
		width: 0.3em;
		height: 0.3em;
	}
	.case{
		margin-top: 1em;
	}
	.case li{
		margin-bottom: 1.3em;
	}
	.case_pic{
		width: 40%;
		height: 4.5em;
		border-radius: 0.2em;
	}
	.case_word{
		width: 53%;
		height: 4.5em;
	}
	.case_word p{
		font-size: 0.875em;
	}
	.case_word span{
		font-size: 0.75em;
	}
	.service{
		margin-top: 1em;
	}
	.service li{
		border-radius: 0.2em;
		margin-bottom: 1em;
	}
	.service li:nth-child(1){
		width: 100%;
		padding: 0.6em;
	}
	.service li:nth-child(n+2){
		width: 48%;
		padding: 0.6em;
	}
	.service li:nth-child(2n){
		margin-right: 4%;
	}
	.service li:nth-child(1) p{
		font-size: 1.125em;
		margin-bottom: 0.1em;
	}
	.service li:nth-child(n+2) p{
		font-size: 0.9375em;
	}
	.pagination{
		padding: 2em 0 6em;
	}
	.pagination li{
		width: 3.5em;
		height: 3.5em;
		line-height: 3.5em;
		margin: 0 0.2em;
		font-size: 0.875em;
	}
	.pagination li:first-child{
		width: 6em;
	}
	.pagination li:last-child{
		width: 6em;
	}
	.page_all{
		font-size: 0.875em;
		margin-left: 1.5em;
	}
	.lead{
		font-size: 0.875em;
		padding: 4em 0;
	}
	.article_title{
		padding-bottom: 1.8em;
	}
	.article_title h1{
		font-size: 1.5em;
	}
	.article_title span{
		font-size: 0.875em;
		margin-top: 1.5em;
	}
	.article_content{
		padding: 2.5em 0;
		line-height: 2.2em;
	}
	.article_content p{
		line-height: 2.2em;
	}
	.article_content img{
	    width:auto !important;
	    height:auto !important;
	    max-width: 100%;
	}
	.go_back{
		padding: 0.8em 2.3em;
		border-radius: 3em;
		margin: 2em 0 4em;
	}
	.go_back span{
		font-size: 0.875em;
		margin-right: 0.2em;
	}
	
	.go_back span a:hover{ color:#fff;}
	.page_link li{
		width: 48%;
	}
	.page_link .page_up{
		margin-right: 4%;
	}
	.page_item{
		width: 14%;
	}
	.page_p{
		width: 86%;
		padding: 1.5em 6%;
	}
	.page_p p{
		font-size: 0.9375em;
		line-height: 1.5em;
		height: 3em;
		margin: 0.8em 0 0.5em;
	}
	.page_p span{
		font-size: 0.875em;
		
	}
	.use_word{ 
		line-height: 1.8em;
	}
	.use_form{
		margin-top: 5em;
	}
	.use_form_title{
		font-size: 2em;
		margin-bottom: 1.25em;
	}
	.use_form_content{
		width: 100%;
	}
	.use_form_content li{
		margin-bottom: 2.5em;
	}
	.use_01{
	    margin-bottom: 1em;
	}
	.use_01 span{
	    font-size: 1.125em;
	}
	.use_01 p{
	    font-size: 1.125em;
		margin-left: 0.5em;
	}
	.use_02 input{
		padding: 0 1.3em;
		height: 3em;
	}
	.use_02 select{
		padding: 0 1em;
		height: 3em;
	}
	.use_02 textarea{
		padding: 1.2em;
		height: 9em;
	}
	.use_wide{
		width: 100%;
	}
	.use_wide01{ 
		width: 49%;
		margin-right: 2%;
	}
	.use_wide02{
		width: 32%;
		margin-right: 2%;
	}
	.use_02 input:last-child{
		margin-right: 0;
	}
	.use_02 select:last-child{
		margin-right: 0;
	}
	.choice{
		margin-top: 1.5em;
	}
	.choice input{ 
		width: 1.2em;
		height: 1.2em;
	}
	.choice span{
		line-height: 1.2em;
		margin-left: 0.8em;
	}
	.use_form_btn{ 
		margin-top: 3em;
	}
	.use_form_btn button{
		width: 20em;
		line-height: 3em;
		border-radius: 0.3em;
	}
}
@media screen and (min-width: 1130px) {
	*{
		font-size: 13px;
	}
	.footer02a li{
		margin-right: 4em;
	}
}
@media screen and (min-width: 1300px) {
	*{
		font-size: 14px;
	}
	.header_pc {
	    margin-left: 35px;
	}
	.searcher{
		padding: 0 15px;
		margin-right: 15px;
	}
	.tell_tu, .code_tu, .earth_tu{
		margin-right: 18px;
	}
	.header_logo span{
		padding-left: 20px;
		margin-left: 20px;
	}
	.footer02a li {
	    margin-right: 5em;
	}
	.question_title p{
		line-height: 45px;
	}
	.question img, .questioner img{
		height: 45px;
	}
	.virtue_pic img{
		height: 45px;
	}
	.scene img{
		height: 28px;
	}
	.scene span{
		line-height: 28px;
	}
	.resource_r{
		top: 1.5em;
	}
}
@media screen and (min-width: 1500px) {
	*{
		font-size: 15px;
	}
	.question_title p{
		font-size: 1.125em;
		line-height: 51px;
		margin-left: 1.2em;
	}
	.question img, .questioner img{
		height: 51px;
	}
	.virtue_word p{
		font-size: 1.125em;
	}
	.virtue_pic img{
		height: 51px;
	}
	.footer01 section{
		margin-left: 1em;
	}
	.footer01 img {
	    width: 3.6875em;
	}
	.brief_pic li img{
		width: auto;
	}
	.policy li{
		height: 18em;
	}
	.scene img{
		height: 30px;
	}
	.scene span{
		line-height: 30px;
		margin-left: 1em;
	}
	.preference2{
		height: 25em;
	}
	.platform_tab li{
		padding: 1em;
	}
	.platform_tab span{
		margin-top: 1em;
	}
	.abut li{
		height: 15.2em;
	}
	.service li:nth-child(1){
		padding: 1em;
	}
	.service li:nth-child(n+2){
		padding: 1em;
	}
}
@media screen and (min-width: 1600px) {
	*{
		font-size: 16px;
	}
	.header_pc {
	    margin-left: 50px;
	}
	.header_pc .header_topic{
		margin: 0 18px;
	}
	.searcher{
		padding: 0 20px;
		margin-right: 20px;
	}
	.login{
		width: 8%;
	}
	.tell_tu, .code_tu, .earth_tu{
		margin-right: 25px;
	}
	.searcher input{
		width: 10em;
		margin-right: 1em;
	}
	.platform_tab li{
		padding: 1.3em;
	}
	.resource_r{
		top: 1em;
	}
	.abut li{
		height: 14.5em;
	}
}
@media screen and (min-width: 1700px) {
	.footer03b_l{
		width: auto;
	}
	.scene li{
		width: 14%;
		margin-right: 7.5%;
	}
	.preference2{
		height: 25em;
	}
	.resource_r{
		top: -2.3em;
	}
	.certificate1{
		width: 32%;
	}
	.certificate2{
		width: 68%;
	}
	.service li:nth-child(1){
		padding: 1.2em;
	}
	.service li:nth-child(n+2){
		padding: 1.2em;
	}
}
@media screen and (max-width: 899px) {
        ul.lhyg_yt{ width:100%; display:flex; flex-wrap:wrap; justify-content:space-between; margin-bottom:40px;}
    ul.lhyg_yt li{width:48%; background:#fff; box-sizing:border-box; padding:20px; font-size:1em; line-height:1em; margin-top:20px; border-radius:6px; text-align:center;}
    ul.lhyg_yt li p{ display:none;}
    
	*{
		font-size: 0.6rem;
	}
	.container{
		width: 90%;
	}
	.header{
		height: 2.3rem;
		padding: 0 4%;
	}
	.header_logo{
		margin-top: 0.4rem;
	}
	.header_logo img{
		width: 5.8rem;
	}
	.header_menu{
		float: right;
		margin-top: 0.7rem;	
	}
	.header_menu img{
		width: 1rem;
	}
	.header_pc,
	.header_tip{
		display: none;
	}
	.searcher,.searcher_line,
	.header_logo span{
		display: none;
	}
	.login{
		float: right;
		margin-top: 0.6rem;
		margin-right: 0.6rem;
		padding: 0.1rem 0.3rem;
		background: #336de2;
		border-radius: 0.3rem;
		display: none;
	}
	.login i{
		display: none;
	}
	.login span{
		color: #fff;
	}
	.searchfor{
		padding: 1.5rem 8%;
		background: #336de2;
	}
	.searchfor section{
		background: #fff;
		border-radius: 3px;
		overflow: hidden;
	}
	.searchfor input{
		float: left;
		line-height: 1.8rem;
		padding-left: 5%;
		font-size: 0.65rem;
		width: 85%;
	}
	.searchfor img{
		float: right;
		margin-top: 0.5rem;
		margin-right: 4%;
	}
	.banner_pic{
		height: 70vh;
	}
	.picture_pic_pc{
		display: none;
	}
	.banner_bg img{
		width: 15rem;
		right: -20%; 
	}
	.banner_word{
		width: 80%;
		left: 5%;
	}
	.banner_word p{
		font-size: 1.2rem;
		line-height: 1.7rem;
	}
	.banner_word span{
		font-size: 0.65rem;
		margin: 0.9rem 0 1.8rem;
	}
	.banner_word button{
		width: 4.2rem;
		line-height: 1.5rem;
		border-radius: 0.2rem;
	}
	.form_bg{
		background: #f7f7f7;
	}
	.banner_form{
		width: 90%;
		margin: -1rem auto 0;
		border-radius: 0.3rem;
	    box-shadow: 0 0 24px #e1e1e1;
	}
	.form_title{
		height: 2rem;
	}
	.form_title li:first-child{
		border-top-left-radius: 0.3rem;
	}
	.form_title li:last-child{ 
		border-top-right-radius: 0.3rem;
	}
	.form_title li p{
		font-size: 0.65rem;
		line-height: 2rem;
	}
	.form_title .form_title_on{
		border-top-left-radius: 0.3rem;
		border-top-right-radius: 0.3rem;
	}
	.form_title .form_title_on p {
	    font-size: 0.7rem;
	    line-height: 2.7rem;
	}
	.form_title .form_title_on p::after{
		width: 30%;
		height: 0.12rem;
		border-radius: 0.2rem;
	}
	.form_ul{
		padding: 1.2rem 5%;
	}
	.edit_text{
		margin-bottom: 0.8rem;
		border-radius: 0.2rem;
	}
	.edit_text input{
		height: 1.8rem;
		line-height: 1.8rem;
	}
	.edit_text1{
		padding-left: 18%;
	}
	.edit_text1 img{
		width: 0.9rem;
	}
	.edit_text1 input{
		width: 90%;
	}
	.form_ul button{
		font-size: 0.6rem;
		line-height: 1.8rem;
		height: 1.8rem;
		border-radius: 0.2rem;
	}
	.edit_tip1{
		margin-top: 0.8rem;
	}
	.edit_title{
		margin-bottom: 0.6rem;
	}
	.edit_text2 input{
	 /*	width: 100%;*/
	 width:50%;
	 margin-left:30%;
		padding: 0 6%;
	}
	.edit_text2 span{
		right: 6%;
	}
	.edit_tip2{
		margin-top: 0.8rem;
		line-height: 1.8rem;
		height: 1.8rem;
	}
	.banner_adv{
		background: #f7f7f7;
		padding: 2rem 0;
	}
	.banner_adv ul{
		flex-wrap: wrap;
		padding: 1.8rem 2% 0.3rem;
		border-radius: 0.3rem;
		box-shadow: 0 0 24px #e1e1e1;
	}
	.banner_adv li{
		width: 50%;
		padding: 0 5%;
		margin-bottom: 1.8rem;
	}
	.banner_adv li img{
		width: 2rem;
	}
	.banner_adv section{
		width: 100%;
		margin-top: 0.5rem;
	}
	.banner_adv p{
		font-size: 0.65rem;
	}
	.banner_adv span{
		margin-top: 0.3rem;
		font-size: 0.55rem;
		line-height: 0.8rem;
	}
	.banner_adv i{
		display: none;
	}
	.wide{
		width: 50%;
	}
	.wide1{ 
		width: 65%;
	}
	.wide2{ 
		width: 35%;
	}
	.wide3{ 
		width: 100%;
	}
	.wide4{ 
		width: 100%;
	}
	
	#backup{ position:fixed; display:block; top:70%; right:1%; width:2rem; height:2rem; background:rgba(0,0,0,0.2);border-radius:1rem;  text-align:center;  z-index:1000;}
	#backup a{ width:1.6rem; height:1.6rem; line-height:1.6rem; display:block; margin:0.2rem; background:rgba(0,0,0,0.4);border-radius:0.8rem;  color:#fff; font-size:0.5rem;}
	.product{
		padding: 2rem 0 2.2rem;
	}
	.product_title{
		margin-bottom: 1.5rem;
	}
	.product_title h1{
		font-size: 0.95rem;
	}
	.product_title p{
		font-size: 0.95rem;
	}
	.serve{
		border-top: solid 1px #e1e1e1;
		border-left: solid 1px #e1e1e1;
	}
	.serve_ul{
		width: 100%;
	}
	.serve_ul li{
		width: 100%;
		padding: 1rem 4rem 1rem 1rem;
	}
	.serve_ul h1{
		font-size: 0.7rem;
	}
	.serve_ul p{
		font-size: 0.55rem;
		line-height: 0.8rem;
		margin: 0.7rem 0;
	}
	.serve_ul .serve_icon{
		width: 1.2rem;
	}
	.serve_ul .serve_img{
		top: 1rem;
		right: 1rem;
	}
	.serve_img img{
		width: 1.5rem;
	}
	.serve_db{
		width: 100%;
		padding: 1.2rem 1rem 0.5rem;
	}
	.serve_db1 h1{
		font-size: 0.7rem;
	}
	.serve_db1 a{
		font-size: 0.55rem;
	}
	.serve_db2{
		padding: 0 5%;
		margin: 1rem 0 0.5rem;
		border-radius: 0.2rem;
	}
	.serve_db2 input{
		font-size: 0.6rem;
		height: 1.8rem;
	}
	.serve_db2 img{
		width: 0.7rem;
	}
	.serve_db3 li{
	    font-size: 0.55rem;
	    padding-left: 5%;
	    height: 1.8rem;
	    line-height: 1.8rem;
	}
	.serve_db3 li::before{
	    width: 0.15rem;
	    height: 0.15rem;
	}
	.solve_line{
		bottom: -1rem;
	}
	.solve{
		padding: 1.5rem 0 0;
	}
	.solve_title{
		width: 100%;
		overflow: hidden;
	}
	.solve_title li{
		width: 50%;
		float: left;
		padding: 0 0.5rem;
	}
	.solve_title .solve_item{
		padding: 0.7rem 0;
	}
	.solve_title img{
		height: 0.8rem;
	}
	.solve_title p{
	    font-size: 0.6rem;
	    line-height: 0.8rem;
	    margin-left: 0.5rem;
	}
	.solve_con{
		width: 100%;
	}
	.solve_ul .solve_infor{
		padding: 1.5rem 0 0;
	}
	.solve_infor1 h1{
		font-size: 0.8rem;
	}
	.solve_infor1 span{
		width: 1rem;
		height: 0.15rem;
		border-radius: 0.2rem;
		margin: 0.8rem 0 1rem;
	}
	.solve_infor1 p{
		line-height: 1.2rem;
	}
	.solve_infor2{
		padding: 1.5rem 0 3rem;
	}
	.solve_infor2 p{
		line-height: 1.5rem;
		margin-right: 0.2rem;
		display: block;
	}
	.solve_infor2 span{
		padding: 0 0.5rem;
		line-height: 1.1rem;
		border-radius: 1rem;
		margin-right: 0.2rem;
		margin-bottom: 0.5rem;
	}
	.solve_infor2 a{
		font-size: 0.55rem;
	    line-height: 1.5rem;
	    padding: 0 0.9rem;
		left: 0;
		bottom: 0;
	}
	.news_title h1,.news_title h2{
		font-size: 0.7rem;
		line-height: 2.4rem;
	}
	.news_title h1::after{
		height: 0.2rem;
		top: 0;
	}
	.news_title span{
		font-size: 0.5rem;
	}
	.news_title img{
		margin-left: 0.2rem;
	}
	.news_a{
		width: 100%;
	}
	.news_a_list{
		margin-top: 0.3rem;
	}
	.news_a_list li{
		margin-bottom: 1rem;
	}
	.news_a_list li:last-child{
		margin-bottom: 0;
	}
	.news_a_pic{
		width: 38%;
		height: 3.2rem;
		border-radius: 0.2rem;
	}
	.news_a_w{
		width: 55%;
		font-size: 0.6rem;
	}
	.news_a_t{
		width: 55%;
		font-size: 0.5rem;
	}
	.news_b{
		width: 100%;
		margin-top: 1.8rem;
	}
	.news_tab{
		width: 100%;
		overflow-x: scroll;
		white-space: nowrap;
		margin-bottom: 0.3rem;
	}
	.news_tab::-webkit-scrollbar{
		display: none;
	}
	.news_tab p{
		display: inline-block;
		font-size: 0.7rem;
		line-height: 2.4rem;
		margin-right: 0.7rem;
	}
	.news_b_list1{
		font-size: 0.7rem;
		margin-bottom: 0.2rem;
	}
	.news_b_list2w {
	    margin-top: 0.7rem;
	    padding-left: 4%;
	}
	.news_b_list2w::before{
		width: 0.15rem;
		height: 0.15rem;
	}
	.news_c{
		width: 100%;
		margin-top: 1.8rem;
		padding: 0 5%;
		   display:none;
	}
	.news_c_list{
		margin-top: 0.2rem;
	}
	.news_c_list li{
		line-height: 1.7rem;
	}
	.news_c_list span{
	    width: 1rem;
	    font-size: 0.6rem;
	    margin-left: 0.2rem;
	}
	.news_c_list p {
	    font-size: 0.6rem;
	    margin-left: 0.2rem;
	}
	.support li:nth-child(1){
		width: 100%;
	}
	.support li:nth-child(2){
		width: 100%;
	}
	.support1{
		width: 100%;
        padding: 1rem;
	}
	.support1 h1{
		font-size: 0.75rem;
	}
	.support1 .support_pic{
		width: 2.4rem;
	}
	.support_icon{
		margin-top: 1rem;
	}
	.support1 .support_icon span{
		font-size: 0.5rem;
	}
	.support_icon img{
		margin-left: 0.1rem;
	}
	.support2{
	    padding: 1.2rem 3rem 1.2rem 1.2rem;
		border-top: solid 1px #dadde5; 
		background: url(../../yibazhan/images/sup_icon03.png) right center no-repeat;
		background-size: auto 55%;
	}
	.support2 h1{
		font-size: 0.75rem;
	}
	.support2 p{
		font-size: 0.55rem;
		line-height: 0.8rem;
	}
	.support2_btn{
		margin-top: 1.2rem;
	}
	.support2_btn button{
		font-size: 0.55rem;
		width: 3.8rem;
		line-height: 1.3rem;
		border-radius: 0.2rem;
		margin-right: 0.4rem;
	}
	.partner1{
		width: 100%;
		border-bottom: solid 1px #dadde5;
	}
	.partner2{
		width: 100%;
	}
	.partner1 ul{
		padding-bottom: 0.5rem;
		overflow: hidden;
	}
	.partner1 li{
		float: left;
		width: 50%;
		height: 5rem;
		text-align: center;
	}
	.partner1 section{
		width: 100%;
	}
	.partner1 h1{
		font-size: 0.75rem;
	}
	.partner1 p{
		font-size: 0.55rem;
		margin-top: 0.1rem;
	}
	.partner2 ul{
		padding: 1rem 0 0;
	}
	.partner2 li{
		width: 33.3%;
		height: 2rem;
		margin-top: 0.5rem;
		text-align: center;
	}
	.partner2 li img{
		width: 3.5rem;
	}
	
	.hzhb ulul{
		padding: 1rem 0 0;
	}
	
	.hzhb li{
		width: 33.3%;
        height: 2rem;
        margin-top: 0.5rem;
        text-align: center;
		float:left;
	}
	.hzhb img{
		width: 3.5rem;
	}
	
	.touch{
		padding: 1.2rem 40% 1.2rem 0;
	}
	.touch h1{
		font-size: 0.8rem;
	}
	.touch p{
		display: none;
	}
	.touch button{
		font-size: 0.55rem;
		line-height: 1.4rem;
		padding: 0 0.6rem;
		border-radius: 0.2rem;
	}
	
	.touch button.pc{ display:none;}
	.touch button.mob{ display:block;}
	.footer01{
		padding: 1.5rem 0 0.3rem;
	}
	.footer01 ul{
		flex-wrap: wrap;
	}
	.footer01 li{
		width: 50%;
		margin-bottom: 1rem;
	}
	.footer01 img{
		width: 2rem;
	}
	.footer01 section{
		justify-content: center;
	}
	.footer01 h1{
		margin-left: 0.5rem;
	}
	.footer01 p{
		display: none;
	}
	.footer02{
		padding: 1.3rem 0;
	}
	.footer02a{
		width: 100%;
	}
	.footer02a li{
		margin-bottom: 0.7rem;
	}
	.footer02a p{
		margin-right: 1.6rem;
	}
	.footer02a span{
		display: none;
	}
	.footer02b{
		width: 100%;
		margin-top: 0.6rem;
	}
	.footer02b h1{
		font-size: 0.7rem;
		margin-bottom: 0.3rem;
	}
	.footer02b_img{
		margin-top: 0.8rem;
		padding-bottom: 0.3rem;
	}
	.footer02b_img img{
		width: 4rem;
		margin-right: 0.9rem;
		border-radius: 0.1rem;
	}
	.footer02b p{
		font-size: 0.55rem;
		margin-top: 0.5rem;
	}
	.footer03{
		padding: 1.3rem 0;
	}
	.footer03a img{
		height: 1.5rem;
		margin-right: 0.5rem;
	}
	.footer03a span{
		display: none;
	}
	.footer03b{
		padding-top: 0.3rem;
	}
	.footer03b_l p{
		font-size: 0.5rem;
		line-height: 0.8rem;
		margin-top: 0.5rem;
	}
	.footer03b_r{
		display: none;
	}
	.footer03a select {
	    font-size: 0.5rem;
	    width: 4.2rem;
	    height: 1.3rem;
	    line-height: 1.3rem;
	    border-radius: 0.2rem;
	    padding: 0 2%;
	}
	.picture_pic{
		height: 55vh;
	}
	.picture_word{
		width: 80%;
		left: 50%;
		transform: translateX(-50%);
		top: 25%;
		text-align: center;
	}
	.picture_word p{
		font-size: 1.2rem;
	}
	.picture_word span {
	    font-size: 0.65rem;
	    margin-top: 0.5rem;
	}
	.picture_word section{
		font-size: 0.65rem;
		line-height: 1rem;
		margin-top: 0.5rem;
	}
	.brief_general{
		margin-top: -0.2rem;
	}
	.brief_l,.brief_r{
		width: 100%;
	}
	.brief_word {
	    line-height: 1.2rem;
	}
	.brief_b2{
		margin-top: 1rem;
	}
	.question ul, .questioner ul{
		flex-wrap: wrap;
	}
	.question li{
		width: 100%;
		padding: 1rem 1.2rem;
		margin-bottom: 0.9rem;
	}
	.question li:last-child{
		margin-bottom: 0;
	}
	.questioner li{
		width: 100%;
		padding: 1rem 1.2rem;
		margin-bottom: 0.8rem;
	}
	.questioner li:last-child{
		margin-bottom: 0;
	}
	.question_title img{
		height: 2rem;
	}
	.question_title p{
		font-size: 0.7rem;
		line-height: 2rem;
		margin-left: 0.6rem;
	}
	.question section,.questioner section{
		font-size: 0.55rem;
		line-height: 1rem;
		margin-top: 0.6rem;
	}
	.question_caption img{
		height: 2rem;
	}
	.question_caption p{
		font-size: 0.7rem;
		margin-top: 0.5rem;
	}
	.plan_t{
		font-size: 0.7rem;
	}
	.plan_ul{
		margin-top: 0.8rem;
	}
	.plan_ul li{
		width: 100%;
		margin-bottom: 1.3rem;
	}
	.plan_ul span {
	    font-size: 0.6rem;
	    padding: 0.1rem 0.3rem;
	    border-radius: 0.2rem;
	    margin-bottom: 0.5rem;
	}
	.plan_ul p{
		font-size: 0.55rem;
		line-height: 1rem;
	}
	.plan_pic,.plan_pic1{
		padding-top: 1.8rem;
	}
	.product_tip{
		font-size: 0.6rem;
		line-height: 1rem;
		margin-top: 0.6rem;
	}
	.analytical{
		margin-top: 0.7rem;
	}
	
	
	
	
	.analytical ul.detail_tab{ width:100%; display:block; border-bottom:1px #ccc solid; margin-bottom:0.5rem; }
    ul.detail_tab li{ padding:0.6rem;width:100%; font-size:0.6rem; box-sizing:border-box; border:none; background:#fff;float:left;}
    ul.detail_tab li:nth-child(1){width:25%; background:#e0e6f2; color:#16171a; }
    ul.detail_tab li:nth-child(2){;width:75%;;  background:#e0e6f2; color:#16171a; }
    ul.detail_tab li:nth-child(3){width:100%; border-bottom:1px #ededed solid;}
    ul.detail_tab li:nth-child(4){width:100%; }

	.analytical ul.detail_tab:nth-child(1){display:none}
	

	table.plzc{ border:1px #ccc solid; width:100%; background:#e9ebf5;}
   .plzc td{ padding:0.3rem ; box-sizing:border-box; background:#fff;}
   .intro-right{ position:relative; padding:1rem 0; width:100%;  text-align:left; font-size:0.6rem;}
   .intro-right span{color:#336de2}
	
	.brief_general img.gtgsqy-pc{display:none;}
    .brief_general img.gtgsqy-m{ display:block;width:100%; height:auto;}
	
	.analytical_ul li{
		width: 100%;
		border-radius: 0.2rem;
	}
	.analytical_ul_other{
	    margin-top: -1rem;
	}
	.analytical_ul_other li{
		width: 100%;
		border-radius: 0.2rem;
	}
	.analytical_line{
		padding: 0.8rem 6%;
	}
	.analytical_ul button{
		padding: 0.3rem 0.7rem;
		border-radius: 1rem;
	}
	.analytical_ul_other li{
	    margin-bottom: 1.3rem;
	}
	.analytical_ul_other li:last-child{
	    margin-bottom: 0;
	}
	.analytical_ul_other button{
		padding: 0.3rem 0.7rem;
		border-radius: 1rem;
	}
	.analytical_ul h1{
		margin: 0.2rem 0 0.7rem;
	}
	.analytical_ul p{
		line-height: 1rem;
	}
	.analytical_ul2{
		margin-top: 1.3rem;
	}
	.analytical_p p{
		padding: 0.3rem 0;
	}
	.analytical_btn{
		width: 100%;
		margin-top: 1.2rem;
	}
	.analytical_btn p{
		width: 82%;
		border-radius: 2rem;
		padding: 0.5rem 0.9rem;
		line-height: 1rem;
	}
	.analytical_line .an_br{
		margin-top: 0.3rem;
	}
	.analytical_line .an_bot{
		margin-bottom: 0.5rem;
	}
	.virtue ul{
		padding-top: 0.5rem;
	}
	.virtue li{
		width: 100%;
		margin-bottom: 1.5rem;
	}
	.virtue li:last-child{
		margin-bottom: 0;
	}
	.virtue .virtue_pic{
		width: 20%;
	}
	.virtue .virtue_word{
		width: 80%;
	}
	.virtue_pic img{
		width: 2rem;
	}
	.virtue_word p{
		font-size: 0.7rem;
		margin-bottom: 0.4rem;
	}
	.virtue_word span{
		font-size: 0.55rem;
		line-height: 0.8rem;
	}
	.client_top{
		padding-top: 2.5rem;
	}
	.client{
	    padding: 1.2rem;
	    background-size: 1.6rem;
	    background-position: left 0.7rem top 0.8rem;
	}
	.client_word{
	    height: 7rem;
	}
	.client_word p{
		-webkit-line-clamp: 7;
		font-size: 0.6rem;
		line-height: 1rem;
	}
	.client_title{
		margin-top: 0.8rem;
	}
	.client_title img{
		height: 1.2rem;
	}
	.client_title p {
	    font-size: 0.65rem;
	    line-height: 1.2rem;
	}
	.client_button_prev{
		margin-right: 0.6rem;
	}
	.client_button img{
		width: 0.9rem;
	}
	.pro_form{
		padding: 1.5rem 0.5rem;
	}
	.pro_form_title h1{
		font-size: 0.9rem;
	}
	.pro_form_title p{
		margin-top: 0.3rem;
	}
	.pro_form_input{
	    width: 100%;
	    margin-top: 1.5rem;
	}
	.pro_form_input input{
	    width: 100%;
	    height: 1.8rem;
	    line-height: 1.8rem;
	    padding: 0 5%;
	    border-radius: 0.2rem;
		margin-bottom: 1rem;
	}
	.pro_form_input button {
	    width: 100%;
	    height: 1.8rem;
	    border-radius: 0.2rem;
	}
	.pro_form_pic{
		display: none;
	}
	.product_btn{
		font-size: 0.55rem;
	    border-radius: 1.5rem;
	    padding: 0.2rem 0.6rem;
	}
	.brief_l .plan_h li{
		margin-bottom: 1.2rem;
	}
	.plan_p1 {
	    font-size: 0.7rem;
	    margin-bottom: 0.6rem;
		padding-left: 6%;
	}
	.plan_p1::after{
		width: 0.2rem;
		border-radius: 0.3rem;
	}
	.plan_p2 {
	    line-height: 1rem;
	}
	.plan_chart{
		margin-bottom: 0.8rem;
	}
	.plan_chart td {
	    padding: 0.7rem 0;
	}
	.brief_pic ul{
		padding-top: 0.8rem;
	}
	.brief_pic li {
	    width: 47%;
	    margin-right: 6%;
	    padding: 1rem 0;
	    border-radius: 0.3rem;
		margin-bottom: 0.9rem;
	}
	.brief_pic li:nth-child(2n){
		margin-right: 0;
	}
	.brief_pic li img{
		width: 3.6rem;
	}
	.brief_pic3 img{
	    margin-top: 0.5rem;
	    margin-bottom: -0.5rem;
	}
	.brief_ot{
		margin-top: 0.9rem;
	}
	.brief_ot_1 {
	    font-size: 0.65rem;
	    margin-bottom: 1rem;
	}
	.brief_ot_2 li {
	    padding-left: 5%;
	    line-height: 0.9rem;
	    margin-bottom: 0.5rem;
	}
	.brief_ot_2 li::after {
	    width: 0.2rem;
	    height: 0.2rem;
	    top: 0.35rem;
	}
	.analytical_item,.analytical_account {
	    padding: 1rem 6%;
	}
	.analytical_item p {
	    padding-left: 5%;
	    line-height: 0.9rem;
		margin-bottom: 0.5rem;
	}
	.analytical_item p::after {
	    width: 0.15rem;
	    height: 0.15rem;
		top: 0.4rem;
	}
	.analytical_account1,.analytical_account2{
		width: 100%;
	}
	.analytical_account2{
		margin-top: 0.8rem;
	}
	.analytical_ul h1 {
		font-size: 0.65rem;
	    margin: 0.3rem 0 0.8rem;
	}
	.analytical_account_h{
	    margin-bottom: 0.6rem;
	}
	.analy_wide p{
		width: 70%;
	}
	.brief_btn{
		margin: 1.3rem 0 0.9rem;
	}
	.brief_btn button {
	    font-size: 0.6rem;
	    padding: 0.2rem 0.7rem;
	    border-radius: 2rem;
	}
	.adapt li {
	    width: 47%;
	    margin-right: 6%;
	    height: 8rem;
	    border-radius: 0.2rem;
		margin-bottom: 0.9rem;
	}
	.adapt li:nth-child(2n){
		margin-right: 0;
	}
	.adapt li:last-child{
		margin-bottom: 0;
	}
	.adapt_word{
		left: 1rem;
		top: 1rem;
	}
	.adapt_word h1{
		font-size: 0.7rem;
	}
	.adapt_word span {
	    width: 0.8rem;
	    height: 0.15rem;
	    border-radius: 0.2rem;
	    margin: 0.6rem 0;
	}
	.adapt_icon{
		right: 1rem;
		bottom: 1.5rem;
	}
	.adapt_icon img{
		width: 1.7rem;
	}
	.path li{
		width: 50%;
		padding: 0.5rem 0;
		margin-bottom: 1rem;
	}
		.pathh{
		font-size: 16px;
		font-weight:bold;
	}
	.path li:not(:last-child){
		background-image: url(../../yibazhan/images/arrow_tu.png);
		background-repeat:no-repeat;
	    background-size: 0.45rem;
	    background-position: right 10% top 1.5rem;
	}
	.path p{
	    font-size: 0.9rem;
	    line-height: 1.2rem;
	}
	.path section{
		width: 58%;
		margin-left: 0.5rem;
	}
	.path h1{
		font-size: 0.7rem;
		line-height: 1.2rem;
	}
	.path span{
		font-size: 0.5rem;
		margin-top: 0.3rem;
	}
	.path h2{
		font-size: 0.5rem;
		line-height: 0.9rem;
		margin-top: 0.3rem;
		width: 90%;
	}
	.path_btn{
		margin-bottom: 1.5rem;
	}
	.path_btn li {
	    padding-bottom: 0.5rem;
	    margin-right: 1.5rem;
	}
	.trade01 li{
	    width: 48%;
	    margin-right: 4%;
	    margin-bottom: 1rem;
	}
	.trade01 .trade_pic{
	    height: 5rem;
	    padding: 1rem;
	}
	.trade01 li:nth-child(2n){
	    margin-right: 0;
	}
	.trade01 li .trade_icon01{
	    right: 0.8rem;
	    bottom: 0.7rem;
	}
	.trade_icon01 img{
	    width: 1.2rem;
	}
	.trade01 li .trade_icon02{
	    left: 1rem;
	    bottom: 1rem;
	}
	.trade_icon02 img{
	    width: 0.8rem;
	}
	.trade02 li {
        width: 48%;
	    margin-right: 4%;
	    margin-bottom: 1rem;
	}
	.trade02 li:nth-child(2n){
	    margin-right: 0;
	}
	.trade02 li .trade_icon01{
	    right: 0.6rem;
	    top: 0.7rem;
	}
	.trade02 .trade_pic{
	    height: 8rem;
	    padding: 0.8rem 0.8rem 0;
	}
	.trade02 .trade_pic p{
	    font-size: 0.7rem;
	}
	.trade02 .trade_pic section{
	    font-size: 0.55rem;
	    line-height: 1rem;
	    margin-top: 1rem;
	}
	.client_space{
		padding-top: 1.5rem;
	}
	.brief_card{
		margin-top: 0.8rem;
	}
	.brief_card li{
		width: 47%;
		padding: 1rem 5%;
		margin-right: 6%;
		text-align: center;
	}
	.brief_card li:nth-child(2n){
		margin-right: 0;
	}
	.brief_card img{
		width: 1.5rem;
	}
	.brief_card p{
		font-size: 0.55rem;
		margin-top: 0.6rem;
		line-height: 0.8rem;
	}
	.policy li{
	    width: 100%;
	    padding: 1rem 5%;
		border-bottom: solid 1px #e1e1e1;
	}
	.policy li:last-child{
		border-bottom: 0;
	}
	.policy h1 {
	    font-size: 0.65rem;
	    padding-left: 0.7rem;
	    margin-bottom: 0.7rem;
	}
	.policy h1::after {
	    width: 0.18rem;
	    height: 75%;
	    border-radius: 0.2rem;
	    top: 18%;
	}
	.policy p {
	    font-size: 0.55rem;
	    padding-left: 0.7rem;
	    line-height: 0.9rem;
	    margin-bottom: 0.3rem;
	}
	.policy p::after {
	    width: 0.15rem;
	    height: 0.15rem;
	    top: 0.35rem;
	}
	.brief_h1 {
	    font-size: 0.65rem;
	    margin-bottom: 1rem;
	}
	.scene{
		margin-top: -0.7rem;
	}
	.scene li{
		width: 50%;
		padding: 0.8rem 0;
		margin-bottom: 0.5rem;
	}
	.scene img{
		height: 1.2rem;
	}
	.scene span{
		line-height: 1.2rem;
		margin-left: 0.3rem;
	}
	.receipt{
		margin-top: 1rem;
	}
	.receipt li {
		font-size: 0.55rem;
	    padding: 0.3rem 0.5rem;
	    border-radius: 2rem;
	    margin-right: 0.4rem;
	}
	.receipt li:last-child{
		margin-right: 0;
	}
	.receipt-bill li {
	    border-radius: 0.2rem;
	    width: 48%;
	    height: 2rem;
	    line-height: 2rem;
	    margin-right: 4%;
	    margin-bottom: 0.6rem;
	}
	.receipt-bill li:nth-child(2n){
		margin-right: 0;
	}
	.guest_title{
		margin-top: 1rem;
		overflow-x: auto;
		white-space: nowrap;
	}
	.guest_title::-webkit-scrollbar {
		display: none;
	}
	.guest_title li{
		display: inline-block;
	    line-height: 1.8rem;
	    margin-right: 0.9rem;
	}
	.guest_pic{
		border-radius: 0.1rem;
		width: 48%;
		height: 3rem;
		line-height: 3rem;
		margin-right: 4%;
		margin-bottom: 0.6rem;
	}
	.guest_pic:nth-child(2n){
		margin-right: 0;
	}
	.packet_btn {
		font-size: 0.55rem;
	    border-radius: 0.15rem;
	    padding: 0.3rem 0.6rem;
	}
	.clause li{
		width: 100%;
		margin-bottom: 1rem;
	}
	.clause_pic {
	    width: 38%;
	    height: 3.5rem;
	    border-radius: 0.15rem;
	}
	.clause_word{
		width: 57%;
		height: 3.5rem;
	}
	.clause_word h1{
		line-height: 0.9rem;
	}
	.clause_word p{
		font-size: 0.55rem;
	}
	.tax-ul{
		padding-top: 0.2rem;
	}
	.tax-ul li{
		width: 100%;
		margin-bottom: 1.6rem;
	}
	.tax-ul li:last-child{
		margin-bottom: 0;
	}
	.tax-title img{
		height: 2rem;
	}
	.tax-title h1 {
	    font-size: 0.7rem;
	    line-height: 2rem;
	    margin-left: 0.8rem;
	}
	.tax-p{
		font-size: 0.55rem;
		line-height: 1rem;
		margin-top: 0.7rem;
	}
	.preference li {
	    border-radius: 0.2rem;
	    width: 100%;
		margin-bottom: 1rem;
	}
	.preference1 {
	    padding: 0.6rem 5%;
	}
	.preference1 button {
	    font-size: 0.55rem;
	    border-radius: 2rem;
	    padding: 0.3rem 0.9rem;
	}
	.preference1 span {
	    font-size: 1.6rem;
	    right: 5%;
	}
	.preference2{
		padding: 0 5% 1rem;
	}
	.preference2 h1 {
	    margin: 1rem 0 0.6rem;
	}
	.preference2 p{
		font-size: 0.55rem;
		line-height: 1rem;
	}
	.resource_bg{
		padding: 0.5rem 0 0;
	}
	.resource_l{
		width: 100%;
	}
	.resource_r{
		float: left;
		width: 100%;
		margin-top: 2rem;
	}
	.resource_w p{
		font-size: 0.55rem;
		line-height: 1.2rem;
		margin-bottom: 0.8rem;
	}
	.principle_l{
		width: 100%;
		padding: 1.2rem 1rem;
	}
	.principle_l button{
		font-size: 0.55rem;
		padding: 0.2rem 0.7rem;
		border-radius: 1rem;
	}
	.principle_l ul{
		margin-top: 1rem;
	}
	.principle_l li {
	    margin-bottom: 1rem;
	    padding-left: 0.8rem;
	}
	.principle_l li::after {
	    width: 0.2rem;
	    height: 0.2rem;
		top: 0.35rem;
	}
	.principle_r{
		width: 100%;
		margin-top: 1.6rem;
	}
	.principle_l li:last-child{
		margin-bottom: 0;
	}
	.suit li {
	    width: 100%;
	    padding: 1rem 7%;
		margin-bottom: 1rem;
	}
	.suit li:last-child{
		margin-bottom: 0;
	}
	.suit h1{
		font-size: 0.7rem;
	}
	.suit i {
		width: 1rem;
		height: 0.15rem;
		border-radius: 0.2rem;
		margin: 0.7rem 0;
	}
	.suit p{
		font-size: 0.55rem;
		line-height: 1rem;
	}
	.suit span {
	    font-size: 1.5rem;
	    top: 0.5rem;
	    right: 7%;
	}
	.banner_record{
		padding: 1rem 0 0;
	}
	.banner_record ul{
		padding: 1.5rem 5%;
		border-radius: 0.3rem;
		flex-wrap: wrap;
	}
	.banner_record li{
		width: 50%;
		padding-left: 3%;
		margin-bottom: 1.5rem;
	}
	.banner_record li:last-child{
		margin-bottom: 0;
	}
	.banner_record section {
	    font-size: 1rem;
	}
	.banner_record i{
	    font-size: 0.7rem;
	    line-height: 0.9rem;
	    margin-left: 0.1rem;
	}
	.banner_record p{
		font-size: 0.55rem;
		margin-top: 0.1rem;
	}
	.about_l{
		width: 100%;
	}

	.about_p {
	    line-height: 1.2rem;
	}
	.about_r {
	    width: 100%;
	    margin-top: 2rem;
	}
	.settle ul{
		padding-top: 0.5rem;
	}
	.settle li{
		width: 100%;
		margin-bottom: 1.5rem;
	}
	.settle li:last-child{
		margin-bottom: 0;
	}
	.settle_pic{
		width: 20%;
	}
	.settle_pic img{
		width: 2rem;
	}
	.settle_w{
		width: 80%;
	}
	.settle_w p{
		font-size: 0.7rem;
		margin-bottom: 0.5rem;
	}
	.settle_w span{
		font-size: 0.55rem;
		line-height: 0.9rem;
	}
	.certificate1{
		width: 100%;
	}
	.certificate_w{
		line-height: 1rem;
	}
	.certificate2{
		width: 100%;
		padding: 1.5rem 0 2.2rem;
	}
	.cert_button img{
		width: 0.9rem;
	}
	.cert_button_prev{
		margin-right: 0.8rem;
	}
	.collaborate1{
		width: 100%;
	}
	.collaborate1 p {
	    font-size: 1rem;
	    line-height: 1.5rem;
	}
	.collaborate1 span {
	    width: 1rem;
	    height: 0.15rem;
	    border-radius: 0.3rem;
	    margin: 0.8rem 0 1.5rem;
	}
	.collaborate2{
	    width: 100%;
	    border-radius: 0.2rem;
	    padding: 1rem 6%;
	}
	
		
	/*about 灰底表单*/
	.collaborate2-1{
	    width: 100%;
	    border-radius: 0.2rem;
	    padding: 1rem 6%;
	}
	.collaborate3a input{
		width: 48%;
		border-radius: 0.3em;
		height: 3.6em;
		line-height: 3.6em;
		margin-right: 4%;
		padding: 0 3%;
	}
	
	.collaborate3a input:nth-child(2n){
		margin-right: 0;
	}
	.collaborate3b{
		margin: 1.8em 0;
	}
	.collaborate3b textarea{
		height: 9em;
		padding: 1.2em 3%;
		border-radius: 0.3em;
	}
	
	
	.collaborate2a input {
	    width: 100%;
	    border-radius: 0.2rem;
	    height: 1.8rem;
	    line-height: 1.8rem;
	    padding: 0 6%;
		margin-bottom: 0.8rem;
	}
	.collaborate2b textarea {
	    height: 5rem;
	    padding: 1rem 6%;
	    border-radius: 0.2rem;
		margin-bottom: 0.8rem;
	}
	.collaborate2c button {
	    width: 100%;
	    line-height: 1.8rem;
	    height: 1.8rem;
	    border-radius: 0.2rem;
	}
	.collaborate3{
		float: left;
		width: 100%;
		margin-top: 1.5rem;
	}
	.collaborate3 p {
	    font-size: 0.65rem;
	    margin-bottom: 0.2rem;
	}
	.collaborate3 span {
	    font-size: 1.2rem;
	}
	.contact1{
		width: 100%;
		height: 9rem;
	}
	.contact2{
		float: left;
		width: 100%;
		margin-top: 1.8rem;
	}
	.contact2 li{
		margin-bottom: 1.2rem;
	}
	.contact2 p{
		font-size: 0.65rem;
	}
	.contact2 span{
		margin-top: 0.3rem;
		line-height: 1rem;
	}
	.content_l{
		width: 100%;
	}
	.information_title{
		padding: 1.5rem 0 0.7rem;
	}
	.information_title ul{
		overflow-x: auto;
		white-space: wrap;
	}
	.information_title ul::-webkit-scrollbar {
		display: none;
	}
	.information_title li{
		display: inline-block;
		margin-right: 0.8rem;
		margin-bottom:0.8rem;
	}
	.information_list li{
		padding: 1.2rem 0;
		border-bottom: solid 1px #e1e1e1;
	}
	.information_list li:last-child{
		border-bottom: 0;
	}
	.information_pic {
		width: 34%;
		height: 3.5rem;
		border-radius: 0.15rem;
	}
	.information_w{
		width: 61%;
		height: 3.5rem;
	}
	.information_w p {
	    font-size: 0.5rem;
	    line-height: 0.7rem;
	    margin-top: 0.2rem;
	}
	.information_w span{
		font-size: 0.5rem;
	}
	.content_r{
		width: 100%;
		margin-top: 1.5rem;
	}
	.sidebar{
		padding-bottom: 1.5rem;
	}
	.sidebar_title{
		height: 2rem;
		line-height: 2rem;
	}
	.sidebar_title span{
		font-size: 0.65rem;
		border-top: solid 0.15rem #336de2;
	}
	.hotspot{
		margin-top: 0.2rem;
	}
	.hotspot li {
		font-size: 0.55rem;
	    padding-left: 5%;
	    line-height: 1.4rem;
	}
	.hotspot li::after {
	    width: 0.15rem;
	    height: 0.15rem;
	}
	.case{
		margin-top: 0.7rem;
	}
	.case li{
		margin-bottom: 0.8rem;
	}
	.case_pic {
	    width: 35%;
	    height: 3rem;
	    border-radius: 0.1rem;
	}
	.case_word {
	    width: 60%;
	    height: 3rem;
	}
	.case_word p{
		font-size: 0.55rem;
		line-height: 0.9rem;
	}
	.case_word span{
		font-size: 0.5rem;
	}
	.service{
		margin-top: 0.7rem;
	}
	.service li{
		padding: 0.7rem 5%;
		margin-bottom: 0.6rem;
		border-radius: 0.2rem;
	}
	.service li:nth-child(1){
		width: 100%;
	}
	.service li:nth-child(1) p{
		font-size: 0.7rem;
		margin-bottom: 0.1rem;
	}
	.service li img{
		width: 1.3rem;
	}
	.service li:nth-child(n+2){
		width: 48%;
	}
	.service li:nth-child(n+2) p{
		font-size: 0.65rem;
	}
	.service li:nth-child(2n){
		margin-right: 4%;
	}
	.pagination{
		padding: 0.5rem 0 2rem;
	}
	.pagination li{
		font-size: 0.55rem;
		width: 1.5rem;
		height: 1.5rem;
		line-height: 1.5rem;
		margin: 0 0.1rem 0.6rem;
	}
	.pagination li:first-child{
		width: 3rem;
	}
	.pagination li:last-child{
		width: 3rem;
	}
	.page_all{
		padding-top: 0.3rem;
	}
	.lead{
		padding: 1.5rem 0;
		line-height: 1rem;
	}
	.article_title{
		padding-bottom: 1rem;
	}
	.article_title h1{
		font-size: 0.8rem;
		line-height: 1.2rem;
	}
	.article_title span {
		font-size: 0.55rem;
		margin-top: 0.8rem;
    }
	.article_content {
		padding: 1.2rem 0;
		line-height: 1.2rem;
    }
    
    .article_content img{
	    width:auto !important;
	    height:auto !important;
	    max-width: 100%;
	}
	.go_back {
		padding: 0.4rem 1rem;
		border-radius: 1.5rem;
		margin: 0.8rem 0 2.5rem;
	}
	.page_link li{
		width: 100%;
		margin-bottom: 1.2rem;
	}
	.page_item {
		width: 13%;
	}
	.page_p {
	    width: 87%;
	    padding: 0.6rem 5%;
	}
	.page_p h1{
		font-size: 0.65rem;
	}
	.page_p p {
		font-size: 0.55rem;
		line-height: 0.8rem;
		height: 1.6rem;
		margin: 0.3rem 0;
	}
	.page_p span{
		font-size: 0.5rem;
	}
	.count{
		padding: 2rem 0;
	}
	.count_l,.count_r{
		width: 100%;
	}
	.count_title{
		padding-bottom: 1.2rem;
	}
	.count_title h1 {
		font-size: 1rem;
		margin-bottom: 0.5rem;
	}
	.count_ul{
		margin-top: 1.5rem;
	}
	.count_ul li{
		width: 50%;
		margin-bottom: 0.5rem;
	}
	.count_ul li:nth-child(2n+1){
		padding-right: 5%;
	}
	.count_ul li:nth-child(2n){
		padding-left: 5%;
	}
	.count_ul img{
		width: 1.8rem;
	}
	.count_ul p{
		font-size: 0.65rem;
		margin: 0.4rem 0 0.2rem;
	}
	
	.about_l img{ width:100%; height:auto;}
	.count_ul span{
		height: 1.6rem;
		font-size: 0.55rem;
		line-height: 0.8rem;
		margin-top: 0.2rem;
	}
	.counter {
	    border-radius: 0.3rem;
	    padding: 1.3rem 7%;
	}
	.counter button {
	    font-size: 0.65rem;
	    border-radius: 1rem;
	    padding: 0.4rem 0;
	}
	.counter1{
		margin-top: 1.2rem;
	}
	.counter1 li{
		margin-bottom: 0.8rem;
		border-radius: 0.15rem;
		padding: 0 5%;
	}
	.counter1 span{
		line-height: 1.8rem;
	}
	.counter1 input{
		width: 60%;
		height: 1.8rem;
		padding: 0 12% 0 5%;
		border-radius: 0.2rem;
	}
	.counter1 i{
		right: 6%;
	}
	.count_r{
		margin-top: 1rem;
	}
	.counter2 li{
		width: 100%;
	}
	.counter2 p{
		font-size: 0.55rem;
		line-height: 1.8rem;
	}
	.counter2 span{
		font-size: 0.55rem;
		line-height: 1.8rem;
	}
	.handbook{
		padding: 2rem 0;
	}
	.handbook_title{
	    font-size:0.95rem;
	    margin-bottom:1.5rem;
	}
	.handbook li {
	    width: 100%;
	    padding: 1rem;
	    margin-bottom: 1.3rem;
	}
	.handbook_l img{
		height: 1.8rem;
	}
	.handbook_r{
		width: 75%;
	}
	.handbook_r h1{
		font-size: 0.65rem;
	}
	.handbook_r p{
	    font-size: 0.55rem;
	    margin: 0.5rem 0;
	}
	.handbook_r span{
		font-size: 0.5rem;
	}
	.superiority li{
    	width: 100%;
    	height: 5rem;
    	border-radius: 0.2rem;
    	margin-bottom: 1rem;
    	position: relative;
    }
    .superiority01{
        width: 86%;
    	left: 7%;
    	bottom: 1rem;
    }
    .superiority01 h1{
    	font-size: 0.75rem;
    }
    .superiority01 span{
    	margin-top: 0.8rem;
    }
    .superiority02{
    	right: 1rem;
    	top: 1rem;
    }
    .superiority02 img{ 
    	width: 1.3rem;
    }
	.platform_word {
	    font-size: 0.6rem;
	    line-height: 1.2rem;
		margin-top: -0.3rem;
	}
	.platform_title{
		margin-top: 1.3rem;
	}
	.platform_title h1{
		font-size: 0.8rem;
	}
	.platform_title p{
		margin-top: 0.3rem;
	}
	.platform_tab{
		margin: 1.2rem 0 1.5rem;
	}
	.platform_tab li{
		padding: 0.5rem 0.8rem;
		border-radius: 0.3rem;
	}
	.platform_tab span{
		font-size: 0.55rem;
		margin-top: 0.2rem;
	}
	.platform_pic{
		width: 100%;
	}
	.abut li{
		width: 100%;
		padding: 1.2rem 1rem 1.2rem 0;
		margin-bottom: 1rem;
	}
	.abut li:last-child{
		margin-bottom: 0;
	}
	.abut .abut_pic{
		width: 30%;
	}
	.abut_pic img{
		width: 2rem;
	}
	.abut .abut_w{
		width: 70%;
	}
	.abut_w h1{
		font-size: 0.65rem;
	}
	.abut_w p{
		font-size: 0.55rem;
		line-height: 0.9rem;
		margin-top: 0.6rem;
	}
	.use_word{ 
	    line-height: 1.2rem;
	}
	.use_form{
		margin-top: 2rem;
	}
	.use_form_title{
		font-size: 1rem;
		margin-bottom: 1.2rem;
	}
	.use_form_content{
		width: 100%;
	}
	.use_form_content li{
		margin-bottom: 1.5rem;
	}
	.use_01{
	    margin-bottom: 0.8rem;
	}
	.use_01 span{
	    font-size: 0.7rem;
	}
	.use_01 p{
	    font-size: 0.7rem;
		margin-left: 0.2rem;
	}
	.use_02 input{
		padding: 0 0.5rem;
		height: 1.8rem;
		margin-bottom: 0.8rem;
	}
	.use_02 input:last-child{
	    margin-bottom: 0;
	}
	.use_02 select{
		padding: 0 0.5rem;
		height: 1.8rem;
		margin-bottom: 0.8rem;
	}
	.use_02 select:last-child{
	    margin-bottom: 0;
	}
	.use_02 textarea{
		padding: 0.6rem;
		height: 6rem;
	}
	.use_wide{
		width: 100%;
	}
	.use_wide01{ 
		width: 100%;
	}
	.use_wide02{
		width: 100%;
	}
	.choice{
		margin-top: 0.6rem;
	}
	.choice input{ 
		width: 0.8rem;
		height: 0.8rem;
		margin-bottom: 0.3rem;
	}
	.choice span{
		line-height: 0.8rem;
		margin-left: 0.5rem;
	}
	.use_form_btn{ 
		margin-top: 1rem;
	}
	.use_form_btn button{
		width: 100%;
		line-height: 1.8rem;
		border-radius: 0.2rem;
	}
	
	.tipword{
    padding: 0 0.8rem;
    background: rgba(0,0,0,0.5);
    color: #fff;
    font-size: 0.6rem;
    visibility: hidden;
    opacity: 0;
    border-radius: 0.2rem;
    transition: all 0.3s;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 9999999;
    
    .contact img{ width:100% !important; height:auto;}
    
    .tipword01{ visibility: visible; opacity: 1; padding: 0.5rem 0.8rem;}

}